"""
Flask Web Application for VLC Automation System
Provides web interface and REST API endpoints
"""
import os
import json
import logging
from pathlib import Path
from flask import Flask, render_template, request, jsonify, send_from_directory, session, redirect, url_for
from werkzeug.utils import secure_filename
from werkzeug.security import check_password_hash, generate_password_hash
import threading
from datetime import datetime
from functools import wraps

from config.settings import ConfigManager, Playlist, PlaylistItem, ScheduleEntry
from core.vlc_controller import VLCController, PlaybackState
from core.webdav_client import WebDAVClient
from core.scheduler import AutomationScheduler, SchedulerState

logger = logging.getLogger(__name__)

class VLCWebApp:
    """Main Flask web application"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.app = Flask(__name__,
                        template_folder='templates',
                        static_folder='static')

        # Configure session
        self.app.secret_key = 'vlc-automation-secret-key-change-in-production'

        # Authentication configuration
        self.auth_config = {
            'username': 'admin',
            'password_hash': generate_password_hash('qwe123')  # Default password
        }

        # Load translations
        self.translations = self._load_translations()
        
        # Initialize core components
        self.vlc_controller = VLCController(config_manager.system.vlc_path)
        self.webdav_client = WebDAVClient(
            config_manager.webdav.url,
            config_manager.webdav.username,
            config_manager.webdav.password
        )
        self.webdav2_client = WebDAVClient(
            config_manager.webdav2.url,
            config_manager.webdav2.username,
            config_manager.webdav2.password
        )
        self.scheduler = AutomationScheduler(
            config_manager,
            self.vlc_controller,
            self.webdav_client,
            self.webdav2_client
        )
        
        # Setup authentication middleware
        self._setup_auth_middleware()

        # Setup routes
        self._setup_routes()
        
        # Application state
        self.is_running = False

    def _setup_auth_middleware(self):
        """Setup authentication middleware"""
        @self.app.before_request
        def require_auth():
            # Allow access to login page and static files
            if (request.endpoint in ['login', 'static', 'index', 'desktop'] or
                request.path.startswith('/static/')):
                return None

            # Check if user is authenticated for all other routes
            if not session.get('authenticated'):
                if request.is_json or request.path.startswith('/api/'):
                    return jsonify({'error': 'Authentication required'}), 401
                return redirect(url_for('login'))

    def _setup_routes(self):
        """Setup Flask routes"""
        
        # Authentication routes
        @self.app.route('/login', methods=['GET', 'POST'])
        def login():
            if request.method == 'POST':
                username = request.form.get('username')
                password = request.form.get('password')

                if self._check_credentials(username, password):
                    session['authenticated'] = True
                    session['username'] = username
                    return redirect(url_for('index'))
                else:
                    return render_template('login.html', error='Invalid username or password')

            # If already authenticated, redirect to main page
            if session.get('authenticated'):
                return redirect(url_for('index'))

            return render_template('login.html')

        @self.app.route('/logout')
        def logout():
            session.clear()
            return redirect(url_for('login'))

        # Main page with device detection
        @self.app.route('/')
        def index():
            # Get User-Agent header to detect mobile devices
            user_agent = request.headers.get('User-Agent', '').lower()

            # Check for mobile devices
            mobile_keywords = [
                'mobile', 'android', 'iphone', 'ipad', 'ipod',
                'blackberry', 'windows phone', 'opera mini',
                'iemobile', 'mobile safari'
            ]

            is_mobile = any(keyword in user_agent for keyword in mobile_keywords)

            # If mobile device detected, redirect to mobile interface
            if is_mobile:
                return redirect(url_for('mobile'))

            # For desktop devices, check authentication
            if not session.get('authenticated'):
                return redirect(url_for('login'))

            # Show desktop interface for authenticated desktop users
            return render_template('index.html')

        # Desktop page (force desktop version)
        @self.app.route('/desktop')
        def desktop():
            # Check if user is authenticated
            if not session.get('authenticated'):
                return redirect(url_for('login'))
            return render_template('index.html')

        # Mobile page
        @self.app.route('/mobile')
        def mobile():
            # Check if user is authenticated
            if not session.get('authenticated'):
                return redirect(url_for('login'))
            return render_template('mobile.html')

        # Language switching
        @self.app.route('/api/language', methods=['GET'])
        def get_language():
            return jsonify({
                'current_language': self._get_current_language(),
                'available_languages': list(self.translations.keys())
            })

        @self.app.route('/api/language/<lang_code>', methods=['POST'])
        def set_language(lang_code):
            if lang_code in self.translations:
                session['language'] = lang_code
                return jsonify({'success': True, 'language': lang_code})
            else:
                return jsonify({'success': False, 'error': 'Language not supported'}), 400

        @self.app.route('/api/translations/<lang_code>')
        def get_translations(lang_code):
            if lang_code in self.translations:
                return jsonify(self.translations[lang_code])
            else:
                return jsonify({}), 404

        # API Routes
        
        # System status
        @self.app.route('/api/status')
        def get_status():
            return jsonify({
                'vlc': {
                    'state': self.vlc_controller.get_state().value,
                    'volume': self.vlc_controller.get_volume(),
                    'media_info': self.vlc_controller.get_current_media_info(),
                    'current_playlist': self.vlc_controller.current_playlist_name,
                    'repeat': self.vlc_controller.get_repeat_mode()
                },
                'scheduler': self.scheduler.get_status(),
                'webdav': self.webdav_client.get_connection_status(),
                'webdav2': self.webdav2_client.get_connection_status(),
                'system': {
                    'running': self.is_running,
                    'timestamp': datetime.now().isoformat()
                }
            })
        
        # VLC Control
        @self.app.route('/api/vlc/play', methods=['POST'])
        def vlc_play():
            success = self.vlc_controller.play()
            return jsonify({'success': success})

        @self.app.route('/api/vlc/pause', methods=['POST'])
        def vlc_pause():
            success = self.vlc_controller.pause()
            return jsonify({'success': success})

        @self.app.route('/api/vlc/stop', methods=['POST'])
        def vlc_stop():
            success = self.vlc_controller.stop()
            return jsonify({'success': success})
        
        @self.app.route('/api/vlc/next', methods=['POST'])
        def vlc_next():
            success = self.vlc_controller.next_track()
            return jsonify({'success': success})
        
        @self.app.route('/api/vlc/previous', methods=['POST'])
        def vlc_previous():
            success = self.vlc_controller.previous_track()
            return jsonify({'success': success})
        
        @self.app.route('/api/vlc/volume', methods=['POST'])
        def set_volume():
            data = request.get_json()
            volume = data.get('volume', 70)
            success = self.vlc_controller.set_volume(volume)
            return jsonify({'success': success, 'volume': volume})

        @self.app.route('/api/vlc/seek', methods=['POST'])
        def seek_position():
            data = request.get_json()
            percentage = data.get('percentage', 0)
            success = self.vlc_controller.seek_to_percentage(percentage)
            return jsonify({'success': success, 'percentage': percentage})

        @self.app.route('/api/vlc/repeat', methods=['POST'])
        def toggle_repeat():
            """Toggle single track repeat mode"""
            new_state = self.vlc_controller.toggle_repeat_mode()
            return jsonify({'success': True, 'repeat': new_state})

        @self.app.route('/api/vlc/repeat', methods=['GET'])
        def get_repeat():
            """Get current repeat mode status"""
            repeat_state = self.vlc_controller.get_repeat_mode()
            return jsonify({'repeat': repeat_state})

        @self.app.route('/api/vlc/play-file', methods=['POST'])
        def play_single_file():
            """Play a single media file directly"""
            data = request.get_json()
            file_path = data.get('path')
            is_webdav = data.get('is_webdav', False)
            webdav_server = data.get('webdav_server', 1)
            volume = data.get('volume', 70)

            if not file_path:
                return jsonify({'success': False, 'error': 'No file path provided'}), 400

            try:
                # Prepare the file path
                if is_webdav:
                    # Determine which WebDAV server to use
                    if webdav_server == 2 and self.webdav2_client:
                        if not self.webdav2_client.is_connected:
                            return jsonify({'success': False, 'error': 'WebDAV2 not connected'}), 400

                        # Get streaming URL for WebDAV2 file
                        streaming_url = self.webdav2_client.get_streaming_url(file_path)
                        if not streaming_url:
                            return jsonify({'success': False, 'error': 'Could not get WebDAV2 streaming URL'}), 400
                    else:
                        if not self.webdav_client.is_connected:
                            return jsonify({'success': False, 'error': 'WebDAV not connected'}), 400

                        # Get streaming URL for WebDAV file
                        streaming_url = self.webdav_client.get_streaming_url(file_path)
                        if not streaming_url:
                            return jsonify({'success': False, 'error': 'Could not get WebDAV streaming URL'}), 400

                    file_paths = [streaming_url]
                else:
                    # Check if local file exists
                    if not Path(file_path).exists():
                        return jsonify({'success': False, 'error': 'File not found'}), 404

                    file_paths = [file_path]

                # Stop current playback first to ensure clean switching
                self.vlc_controller.stop()

                # Load single file as playlist and play
                if self.vlc_controller.load_playlist(file_paths, shuffle=False):
                    self.vlc_controller.set_volume(volume)
                    success = self.vlc_controller.play()

                    if success:
                        file_name = Path(file_path).name
                        return jsonify({
                            'success': True,
                            'message': f'Playing: {file_name}',
                            'file_name': file_name
                        })
                    else:
                        return jsonify({'success': False, 'error': 'Failed to start playback'}), 500
                else:
                    return jsonify({'success': False, 'error': 'Failed to load file'}), 500

            except Exception as e:
                logger.error(f"Error playing single file {file_path}: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        # Playlist Management
        @self.app.route('/api/playlists')
        def get_playlists():
            playlists = {}
            for name, playlist in self.config_manager.playlists.items():
                playlists[name] = {
                    'name': playlist.name,
                    'items': [{'name': item.name, 'path': item.path, 'is_webdav': item.is_webdav, 'enabled': item.enabled,
                              'webdav_server': getattr(item, 'webdav_server', 1)}
                             for item in playlist.items],
                    'shuffle': playlist.shuffle,
                    'repeat': playlist.repeat,
                    'volume': playlist.volume
                }
            return jsonify(playlists)

        @self.app.route('/api/file-duration', methods=['POST'])
        def get_file_duration():
            """Get duration of a media file"""
            try:
                data = request.get_json()
                file_path = data.get('path')
                is_webdav = data.get('is_webdav', False)
                webdav_server = data.get('webdav_server', 1)

                if not file_path:
                    return jsonify({'success': False, 'error': 'No file path provided'}), 400

                duration = self._get_media_duration(file_path, is_webdav, webdav_server)

                if duration is not None:
                    return jsonify({'success': True, 'duration': duration})
                else:
                    return jsonify({'success': False, 'error': 'Could not retrieve duration'})

            except Exception as e:
                logger.error(f"Error getting file duration: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @self.app.route('/api/playlists', methods=['POST'])
        def create_playlist():
            data = request.get_json()
            try:
                items = [PlaylistItem(**item) for item in data.get('items', [])]
                playlist = Playlist(
                    name=data['name'],
                    items=items,
                    shuffle=data.get('shuffle', False),
                    repeat=data.get('repeat', False),
                    volume=data.get('volume', 70)
                )
                self.config_manager.add_playlist(playlist)
                return jsonify({'success': True, 'message': 'Playlist created'})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 400
        
        @self.app.route('/api/playlists/<name>', methods=['PUT'])
        def update_playlist(name):
            data = request.get_json()
            try:
                items = [PlaylistItem(**item) for item in data.get('items', [])]
                playlist = Playlist(
                    name=data['name'],
                    items=items,
                    shuffle=data.get('shuffle', False),
                    repeat=data.get('repeat', False),
                    volume=data.get('volume', 70)
                )
                self.config_manager.add_playlist(playlist)
                return jsonify({'success': True, 'message': 'Playlist updated'})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 400
        
        @self.app.route('/api/playlists/<name>', methods=['DELETE'])
        def delete_playlist(name):
            try:
                self.config_manager.remove_playlist(name)
                return jsonify({'success': True, 'message': 'Playlist deleted'})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 400

        @self.app.route('/api/playlists/<name>/add-files', methods=['POST'])
        def add_files_to_playlist(name):
            """Add files to an existing playlist"""
            try:
                data = request.get_json()
                files = data.get('files', [])

                playlist = self.config_manager.get_playlist(name)
                if not playlist:
                    return jsonify({'success': False, 'error': 'Playlist not found'}), 404

                # Add new files to the playlist
                for file_data in files:
                    new_item = PlaylistItem(
                        name=file_data.get('name', ''),
                        path=file_data.get('path', ''),
                        is_webdav=file_data.get('is_webdav', False),
                        webdav_server=file_data.get('webdav_server', 1),  # Default to server 1
                        enabled=True
                    )
                    playlist.items.append(new_item)

                # Save the updated playlist
                self.config_manager.add_playlist(playlist)

                return jsonify({
                    'success': True,
                    'message': f'Added {len(files)} files to playlist "{name}"'
                })

            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 400
        
        @self.app.route('/api/playlists/<name>/play', methods=['POST'])
        def play_playlist(name):
            try:
                playlist = self.config_manager.get_playlist(name)
                if not playlist:
                    return jsonify({'success': False, 'error': 'Playlist not found'}), 404

                # Stop current playback first to ensure clean switching
                self.vlc_controller.stop()

                # Prepare file paths
                file_paths = []
                skipped_files = []
                for item in playlist.items:
                    if not item.enabled:
                        continue

                    if item.is_webdav:
                        # Determine which WebDAV server to use
                        webdav_server = getattr(item, 'webdav_server', 1)
                        if webdav_server == 2 and self.webdav2_client:
                            if self.webdav2_client.is_connected:
                                url = self.webdav2_client.get_streaming_url(item.path)
                                if url:
                                    file_paths.append(url)
                                    logger.debug(f"Added WebDAV2 file to playlist: {item.path}")
                                else:
                                    skipped_files.append(f"WebDAV2: {item.name}")
                            else:
                                skipped_files.append(f"WebDAV2 not connected: {item.name}")
                        else:
                            if self.webdav_client.is_connected:
                                url = self.webdav_client.get_streaming_url(item.path)
                                if url:
                                    file_paths.append(url)
                                    logger.debug(f"Added WebDAV file to playlist: {item.path}")
                                else:
                                    skipped_files.append(f"WebDAV: {item.name}")
                            else:
                                skipped_files.append(f"WebDAV not connected: {item.name}")
                    else:
                        if Path(item.path).exists():
                            file_paths.append(item.path)
                            logger.debug(f"Added local file to playlist: {item.path}")
                        else:
                            skipped_files.append(f"Local file not found: {item.name}")

                if skipped_files:
                    logger.warning(f"Skipped {len(skipped_files)} files from playlist {name}: {skipped_files[:3]}{'...' if len(skipped_files) > 3 else ''}")
                
                if not file_paths:
                    return jsonify({'success': False, 'error': 'No valid files in playlist'}), 400
                
                # Load and play
                if self.vlc_controller.load_playlist(file_paths, playlist.shuffle, playlist.name):
                    self.vlc_controller.set_volume(playlist.volume)
                    success = self.vlc_controller.play()
                    return jsonify({'success': success})
                else:
                    return jsonify({'success': False, 'error': 'Failed to load playlist'}), 500
                
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500

        @self.app.route('/api/playlists/<name>/play-item', methods=['POST'])
        def play_playlist_item(name):
            try:
                data = request.get_json()
                item_index = data.get('index', 0)

                playlist = self.config_manager.get_playlist(name)
                if not playlist:
                    return jsonify({'success': False, 'error': 'Playlist not found'}), 404

                if item_index < 0 or item_index >= len(playlist.items):
                    return jsonify({'success': False, 'error': 'Invalid item index'}), 400

                item = playlist.items[item_index]
                if not item.enabled:
                    return jsonify({'success': False, 'error': 'Item is disabled'}), 400

                # Stop current playback first
                self.vlc_controller.stop()

                # Get file path/URL
                file_path = None
                if item.is_webdav:
                    webdav_server = getattr(item, 'webdav_server', 1)
                    if webdav_server == 2 and self.webdav2_client and self.webdav2_client.is_connected:
                        file_path = self.webdav2_client.get_streaming_url(item.path)
                    elif self.webdav_client and self.webdav_client.is_connected:
                        file_path = self.webdav_client.get_streaming_url(item.path)

                    if not file_path:
                        return jsonify({'success': False, 'error': 'WebDAV not connected or file not accessible'}), 400
                else:
                    if Path(item.path).exists():
                        file_path = item.path
                    else:
                        return jsonify({'success': False, 'error': 'Local file not found'}), 400

                # Play the single file
                if self.vlc_controller.load_playlist([file_path], False, f"{name} - {item.name}"):
                    self.vlc_controller.set_volume(playlist.volume)
                    success = self.vlc_controller.play()
                    return jsonify({'success': success})
                else:
                    return jsonify({'success': False, 'error': 'Failed to load file'}), 500

            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500

        # Schedule Management
        @self.app.route('/api/schedules')
        def get_schedules():
            schedules = []
            for schedule in self.config_manager.schedules:
                schedules.append({
                    'name': schedule.name,
                    'start_time': schedule.start_time,
                    'end_time': schedule.end_time,
                    'playlist_name': schedule.playlist_name,
                    'days_of_week': schedule.days_of_week,
                    'enabled': schedule.enabled,
                    'volume': schedule.volume
                })
            return jsonify(schedules)
        
        @self.app.route('/api/schedules', methods=['POST'])
        def create_schedule():
            data = request.get_json()
            try:
                schedule = ScheduleEntry(**data)
                self.config_manager.add_schedule(schedule)
                self.scheduler.reload_schedules()
                return jsonify({'success': True, 'message': 'Schedule created'})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 400
        
        @self.app.route('/api/schedules/<name>', methods=['PUT'])
        def update_schedule(name):
            data = request.get_json()
            try:
                schedule = ScheduleEntry(**data)
                self.config_manager.update_schedule(name, schedule)
                self.scheduler.reload_schedules()
                return jsonify({'success': True, 'message': 'Schedule updated'})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 400
        
        @self.app.route('/api/schedules/<name>', methods=['DELETE'])
        def delete_schedule(name):
            try:
                self.config_manager.remove_schedule(name)
                self.scheduler.reload_schedules()
                return jsonify({'success': True, 'message': 'Schedule deleted'})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 400
        
        # Scheduler Control
        @self.app.route('/api/scheduler/start', methods=['POST'])
        def start_scheduler():
            try:
                self.scheduler.start()
                return jsonify({'success': True, 'message': 'Scheduler started'})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @self.app.route('/api/scheduler/stop', methods=['POST'])
        def stop_scheduler():
            try:
                self.scheduler.stop()
                return jsonify({'success': True, 'message': 'Scheduler stopped'})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @self.app.route('/api/scheduler/pause', methods=['POST'])
        def pause_scheduler():
            try:
                self.scheduler.pause()
                return jsonify({'success': True, 'message': 'Scheduler paused'})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @self.app.route('/api/scheduler/resume', methods=['POST'])
        def resume_scheduler():
            try:
                self.scheduler.resume()
                return jsonify({'success': True, 'message': 'Scheduler resumed'})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500

        @self.app.route('/api/scheduler/reload', methods=['POST'])
        def reload_scheduler():
            try:
                self.scheduler.reload_schedules()
                return jsonify({'success': True, 'message': 'Schedules reloaded'})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500

        @self.app.route('/api/scheduler/force-start/<schedule_name>', methods=['POST'])
        def force_start_schedule(schedule_name):
            try:
                success = self.scheduler.force_start_schedule(schedule_name)
                if success:
                    return jsonify({'success': True, 'message': f'Schedule "{schedule_name}" started'})
                else:
                    return jsonify({'success': False, 'error': 'Schedule not found or could not start'}), 404
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500

        @self.app.route('/api/scheduler/force-stop', methods=['POST'])
        def force_stop_current():
            try:
                success = self.scheduler.force_stop_current()
                if success:
                    return jsonify({'success': True, 'message': 'Current playback stopped'})
                else:
                    return jsonify({'success': False, 'error': 'No active playback to stop'}), 404
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500
        
        # File Browser
        @self.app.route('/api/files/local')
        def browse_local_files():
            path = request.args.get('path', 'data')
            try:
                files = []
                path_obj = Path(path)
                
                if path_obj.exists() and path_obj.is_dir():
                    for item in path_obj.iterdir():
                        if item.name.startswith('.'):
                            continue
                        
                        files.append({
                            'name': item.name,
                            'path': str(item),
                            'is_dir': item.is_dir(),
                            'is_media': self._is_media_file(item) if item.is_file() else False,
                            'size': item.stat().st_size if item.is_file() else 0
                        })
                
                return jsonify({'files': files, 'current_path': str(path_obj.absolute())})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @self.app.route('/api/files/webdav')
        def browse_webdav_files():
            path = request.args.get('path', '/')
            try:
                if not self.webdav_client.is_connected:
                    return jsonify({'success': False, 'error': 'WebDAV not connected'}), 400

                files = self.webdav_client.list_directory(path)
                file_list = [file.to_dict() for file in files]

                return jsonify({'files': file_list, 'current_path': path})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500

        @self.app.route('/api/files/webdav2')
        def browse_webdav2_files():
            path = request.args.get('path', '/')
            try:
                if not self.webdav2_client.is_connected:
                    return jsonify({'success': False, 'error': 'WebDAV2 not connected'}), 400

                files = self.webdav2_client.list_directory(path)
                file_list = [file.to_dict() for file in files]

                return jsonify({'files': file_list, 'current_path': path})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500
        
        # WebDAV Configuration
        @self.app.route('/api/webdav/config', methods=['GET'])
        def get_webdav_config():
            return jsonify({
                'url': self.config_manager.webdav.url,
                'username': self.config_manager.webdav.username,
                'enabled': self.config_manager.webdav.enabled,
                'connected': self.webdav_client.is_connected
            })
        
        @self.app.route('/api/webdav/config', methods=['POST'])
        def update_webdav_config():
            data = request.get_json()
            try:
                self.config_manager.webdav.url = data.get('url', '')
                self.config_manager.webdav.username = data.get('username', '')
                self.config_manager.webdav.password = data.get('password', '')
                self.config_manager.webdav.enabled = data.get('enabled', False)
                
                self.config_manager.save_system_config()
                
                # Reconnect WebDAV if enabled
                if self.config_manager.webdav.enabled:
                    self.webdav_client.disconnect()
                    self.webdav_client = WebDAVClient(
                        self.config_manager.webdav.url,
                        self.config_manager.webdav.username,
                        self.config_manager.webdav.password
                    )
                
                return jsonify({'success': True, 'message': 'WebDAV configuration updated'})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @self.app.route('/api/webdav/test', methods=['POST'])
        def test_webdav_connection():
            try:
                success, message = self.webdav_client.test_connection()
                return jsonify({'success': success, 'message': message})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500

        @self.app.route('/api/webdav/reconnect', methods=['POST'])
        def reconnect_webdav():
            try:
                # Disconnect current connection
                self.webdav_client.disconnect()

                # Create new connection with current config
                self.webdav_client = WebDAVClient(
                    self.config_manager.webdav.url,
                    self.config_manager.webdav.username,
                    self.config_manager.webdav.password
                )

                # Test the new connection
                if self.webdav_client.is_connected:
                    return jsonify({'success': True, 'message': 'WebDAV reconnected successfully'})
                else:
                    return jsonify({'success': False, 'message': 'Failed to reconnect WebDAV'})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500

        # WebDAV2 Configuration
        @self.app.route('/api/webdav2/config', methods=['GET'])
        def get_webdav2_config():
            return jsonify({
                'url': self.config_manager.webdav2.url,
                'username': self.config_manager.webdav2.username,
                'enabled': self.config_manager.webdav2.enabled,
                'connected': self.webdav2_client.is_connected
            })

        @self.app.route('/api/webdav2/config', methods=['POST'])
        def update_webdav2_config():
            data = request.get_json()
            try:
                self.config_manager.webdav2.url = data.get('url', '')
                self.config_manager.webdav2.username = data.get('username', '')
                self.config_manager.webdav2.password = data.get('password', '')
                self.config_manager.webdav2.enabled = data.get('enabled', False)

                self.config_manager.save_system_config()

                # Reconnect WebDAV2 if enabled
                if self.config_manager.webdav2.enabled:
                    self.webdav2_client.disconnect()
                    self.webdav2_client = WebDAVClient(
                        self.config_manager.webdav2.url,
                        self.config_manager.webdav2.username,
                        self.config_manager.webdav2.password
                    )

                return jsonify({'success': True, 'message': 'WebDAV2 configuration updated'})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500

        @self.app.route('/api/webdav2/test', methods=['POST'])
        def test_webdav2_connection():
            try:
                success, message = self.webdav2_client.test_connection()
                return jsonify({'success': success, 'message': message})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500

        @self.app.route('/api/webdav2/reconnect', methods=['POST'])
        def reconnect_webdav2():
            try:
                # Disconnect current connection
                self.webdav2_client.disconnect()

                # Create new connection with current config
                self.webdav2_client = WebDAVClient(
                    self.config_manager.webdav2.url,
                    self.config_manager.webdav2.username,
                    self.config_manager.webdav2.password
                )

                # Test the new connection
                if self.webdav2_client.is_connected:
                    return jsonify({'success': True, 'message': 'WebDAV2 reconnected successfully'})
                else:
                    return jsonify({'success': False, 'message': 'Failed to reconnect WebDAV2'})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500

        # Settings API
        @self.app.route('/api/settings', methods=['GET'])
        def get_settings():
            return jsonify({
                'success': True,
                'settings': {
                    'webdav': {
                        'url': self.config_manager.webdav.url,
                        'username': self.config_manager.webdav.username,
                        'enabled': self.config_manager.webdav.enabled,
                        'connected': self.webdav_client.is_connected
                    },
                    'webdav2': {
                        'url': self.config_manager.webdav2.url,
                        'username': self.config_manager.webdav2.username,
                        'enabled': self.config_manager.webdav2.enabled,
                        'connected': self.webdav2_client.is_connected
                    },
                    'system': {
                        'vlc_path': self.config_manager.system.vlc_path,
                        'web_port': self.config_manager.system.web_port,
                        'auto_start': self.config_manager.system.auto_start
                    }
                }
            })
    
    def _is_media_file(self, file_path: Path) -> bool:
        """Check if file is a media file"""
        media_extensions = {
            # Audio formats
            '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a', '.aiff', '.au', '.ra',
            # Video formats
            '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.mpg', '.mpeg'
        }
        return file_path.suffix.lower() in media_extensions

    def _load_translations(self):
        """Load translation files"""
        translations = {}
        translations_dir = Path(__file__).parent / 'static' / 'translations'

        for lang_file in translations_dir.glob('*.json'):
            lang_code = lang_file.stem
            try:
                with open(lang_file, 'r', encoding='utf-8') as f:
                    translations[lang_code] = json.load(f)
            except Exception as e:
                logger.error(f"Failed to load translation file {lang_file}: {e}")

        return translations

    def _get_current_language(self):
        """Get current language from session or default to English"""
        return session.get('language', 'en')

    def _login_required(self, f):
        """Decorator to require authentication for routes"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not session.get('authenticated'):
                if request.is_json:
                    return jsonify({'error': 'Authentication required'}), 401
                return redirect(url_for('login'))
            return f(*args, **kwargs)
        return decorated_function

    def _check_credentials(self, username, password):
        """Check if provided credentials are valid"""
        return (username == self.auth_config['username'] and
                check_password_hash(self.auth_config['password_hash'], password))

    def _get_media_duration(self, file_path: str, is_webdav: bool = False, webdav_server: int = 1):
        """Get duration of a media file in seconds"""
        try:
            import vlc

            # Create a temporary VLC instance for duration checking
            temp_instance = vlc.Instance('--intf', 'dummy', '--no-video', '--quiet')

            if is_webdav:
                # Get streaming URL for WebDAV files
                if webdav_server == 2 and self.webdav2_client and self.webdav2_client.is_connected:
                    url = self.webdav2_client.get_streaming_url(file_path)
                    if not url:
                        return None
                    media_path = url
                elif self.webdav_client and self.webdav_client.is_connected:
                    url = self.webdav_client.get_streaming_url(file_path)
                    if not url:
                        return None
                    media_path = url
                else:
                    return None
            else:
                # Local file
                if not Path(file_path).exists():
                    return None
                media_path = file_path

            # Create media and get duration
            media = temp_instance.media_new(media_path)
            media.parse()

            # Wait a bit for parsing to complete
            import time
            timeout = 5  # 5 second timeout
            start_time = time.time()

            while media.get_duration() == -1 and (time.time() - start_time) < timeout:
                time.sleep(0.1)

            duration_ms = media.get_duration()

            # Clean up
            media.release()
            temp_instance.release()

            if duration_ms > 0:
                return duration_ms // 1000  # Convert to seconds
            else:
                return None

        except Exception as e:
            logger.error(f"Error getting duration for {file_path}: {e}")
            return None

    def run(self, host='0.0.0.0', port=None, debug=False):
        """Run the Flask application"""
        if port is None:
            port = self.config_manager.system.web_port
        
        self.is_running = True
        
        # Start scheduler if auto_start is enabled
        if self.config_manager.system.auto_start:
            self.scheduler.start()
        
        logger.info(f"Starting web server on {host}:{port}")
        self.app.run(host=host, port=port, debug=debug, threaded=True)
    
    def shutdown(self):
        """Shutdown the application"""
        self.is_running = False
        self.scheduler.stop()
        self.vlc_controller.cleanup()
        self.webdav_client.disconnect()
        self.webdav2_client.disconnect()
        logger.info("VLC Web App shutdown complete")
