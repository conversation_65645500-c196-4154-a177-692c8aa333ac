# VLC Automation System - Windows PowerShell Quick Start Script
# This script sets up the environment and starts the VLC automation system

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "VLC Automation System - Quick Start" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if Python is installed
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -ne 0) {
        throw "Python not found"
    }
    Write-Host "Python found: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Python 3.8 or later from https://python.org" -ForegroundColor Yellow
    Write-Host "Make sure to check 'Add Python to PATH' during installation" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if we're in the correct directory
if (-not (Test-Path "main.py")) {
    Write-Host "ERROR: main.py not found in current directory" -ForegroundColor Red
    Write-Host "Please run this script from the VLC automation project directory" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Checking dependencies..." -ForegroundColor Yellow

# Check if virtual environment exists
if (-not (Test-Path "venv")) {
    Write-Host "Creating virtual environment..." -ForegroundColor Yellow
    python -m venv venv
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Failed to create virtual environment" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

# Activate virtual environment
Write-Host "Activating virtual environment..." -ForegroundColor Yellow
& "venv\Scripts\Activate.ps1"
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to activate virtual environment" -ForegroundColor Red
    Write-Host "You may need to enable PowerShell script execution:" -ForegroundColor Yellow
    Write-Host "Run: Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Install/update dependencies
Write-Host "Installing dependencies..." -ForegroundColor Yellow
pip install -r requirements.txt
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to install dependencies" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Dependencies installed successfully!" -ForegroundColor Green

# Check if configuration exists
if (-not (Test-Path "config\config.json")) {
    Write-Host ""
    Write-Host "No configuration found. Creating sample configuration..." -ForegroundColor Yellow
    python main.py sample-config
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Failed to create sample configuration" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
    Write-Host ""
    Write-Host "Sample configuration created!" -ForegroundColor Green
    Write-Host "Please edit the configuration files in the 'config' directory before starting." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Configuration files:" -ForegroundColor Cyan
    Write-Host "- config\config.json: System and WebDAV settings" -ForegroundColor White
    Write-Host "- config\playlists.json: Playlist definitions" -ForegroundColor White
    Write-Host "- config\schedules.json: Schedule definitions" -ForegroundColor White
    Write-Host ""
    Read-Host "Press Enter to continue with default settings or Ctrl+C to exit and configure first"
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Starting VLC Automation System..." -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "The web interface will be available at:" -ForegroundColor Green
Write-Host "http://localhost:5000" -ForegroundColor White
Write-Host ""
Write-Host "Press Ctrl+C to stop the system" -ForegroundColor Yellow
Write-Host ""

# Start the application
try {
    python main.py start
} catch {
    Write-Host ""
    Write-Host "Application stopped with error: $_" -ForegroundColor Red
} finally {
    Write-Host ""
    Write-Host "VLC Automation System has stopped." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
}
