/* VLC Automation System - Modern Minimalist Styles */

:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --background: #f8fafc;
    --surface: #ffffff;
    --surface-hover: #f1f5f9;
    --border: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --radius: 8px;
    --radius-lg: 12px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Noto Sans CJK SC', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif;
    background-color: var(--background);
    color: var(--text-primary);
    line-height: 1.6;
    width: 100%;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    width: 100%;
    box-sizing: border-box;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--surface);
    padding: 20px 30px;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 15px;
    min-height: 80px;
    width: 100%;
    box-sizing: border-box;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
    min-width: 0;
    flex-shrink: 0;
}

.header h1 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--primary-color);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Noto Sans CJK SC', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif;
    word-break: break-word;
    overflow-wrap: break-word;
    line-height: 1.3;
    margin: 0;
    flex: 1;
    min-width: 200px;
}

.header h1 i {
    margin-right: 10px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--success-color);
    animation: pulse 2s infinite;
}

.status-dot.error {
    background: var(--danger-color);
}

.status-dot.warning {
    background: var(--warning-color);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Language Switcher */
.language-switcher {
    position: relative;
    flex-shrink: 0;
}

.language-select {
    padding: 8px 12px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--surface);
    color: var(--text-primary);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 100px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Noto Sans CJK SC', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif;
}

.language-select:hover {
    border-color: var(--primary-color);
}

.language-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* User Controls */
.user-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;
}

.logout-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: var(--radius);
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    text-decoration: none;
    transition: all 0.2s ease;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.logout-btn:hover {
    background: rgba(239, 68, 68, 0.2);
    transform: translateY(-1px);
}

.logout-btn:active {
    transform: translateY(0);
}

.mobile-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
    border-radius: var(--radius-md);
    text-decoration: none;
    transition: all 0.2s ease;
    border: 1px solid rgba(37, 99, 235, 0.2);
}

.mobile-btn:hover {
    background: rgba(37, 99, 235, 0.2);
    transform: translateY(-1px);
}

.mobile-btn:active {
    transform: translateY(0);
}

/* Main Content */
.main-content {
    display: grid;
    gap: 35px;
    padding: 10px 0;
}

/* Control Panel */
.control-panel, .scheduler-panel {
    background: var(--surface);
    padding: 30px;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--border);
    transition: all 0.2s ease;
    margin-bottom: 20px;
    width: 100%;
    box-sizing: border-box;
}

.control-panel:hover, .scheduler-panel:hover {
    box-shadow: var(--shadow-lg);
    border-color: rgba(37, 99, 235, 0.1);
}

.control-panel h2, .scheduler-panel h2 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 25px;
    color: var(--text-primary);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Noto Sans CJK SC', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--border);
}

.media-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}

.control-btn {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: var(--radius);
    background: var(--primary-color);
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    min-width: 50px;
    min-height: 50px;
}

.control-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.control-btn:active {
    transform: translateY(0);
}

.control-btn.active {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.4);
}

.control-btn.active:hover {
    background: var(--primary-hover);
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    justify-content: center;
}

.volume-slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: var(--border);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
}

.volume-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
}

/* Progress Control */
.progress-control {
    margin-bottom: 20px;
}

.progress-bar-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.progress-bar {
    position: relative;
    height: 8px;
    background: var(--surface-hover);
    border-radius: 4px;
    cursor: pointer;
    user-select: none;
}

.progress-fill {
    height: 100%;
    background: var(--primary-color);
    border-radius: 4px;
    width: 0%;
    transition: width 0.1s ease;
}

.progress-handle {
    position: absolute;
    top: 50%;
    left: 0%;
    width: 16px;
    height: 16px;
    background: var(--primary-color);
    border: 2px solid white;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    cursor: grab;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.progress-bar:hover .progress-handle {
    opacity: 1;
}

.progress-handle:active {
    cursor: grabbing;
    transform: translate(-50%, -50%) scale(1.2);
}

.time-display {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: var(--text-secondary);
    font-family: 'Courier New', monospace;
}

.current-media {
    background: linear-gradient(135deg, var(--surface-hover) 0%, var(--surface) 100%);
    padding: 25px;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border);
    box-shadow: var(--shadow);
    transition: all 0.2s ease;
    margin-top: 20px;
}

.current-media:hover {
    box-shadow: var(--shadow-lg);
    border-color: rgba(37, 99, 235, 0.2);
}

.media-title {
    font-weight: 600;
    margin-bottom: 5px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Noto Sans CJK SC', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif;
    word-break: break-word;
    overflow-wrap: break-word;
    line-height: 1.4;
}

.media-details {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Scheduler Panel */
.scheduler-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.scheduler-status {
    display: grid;
    gap: 10px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid var(--border);
}

.status-item:last-child {
    border-bottom: none;
}

.status-item .label {
    font-weight: 500;
    color: var(--text-secondary);
}

.status-item .value {
    color: var(--text-primary);
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: var(--radius);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-size: 0.9rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #475569;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #059669;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background: #d97706;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
}

/* Tabs */
.tabs {
    display: flex;
    background: var(--surface);
    border-radius: var(--radius-lg);
    padding: 8px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border);
    flex-wrap: wrap;
    gap: 5px;
}

.tab-btn {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: var(--radius);
    transition: all 0.2s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-width: 120px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Noto Sans CJK SC', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif;
}

.tab-btn.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow);
    transform: translateY(-1px);
}

.tab-btn:hover:not(.active) {
    background: var(--surface-hover);
    color: var(--text-primary);
    transform: translateY(-1px);
}

/* Tab Content */
.tab-content {
    background: var(--surface);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    min-height: 500px;
    width: 100%;
    box-sizing: border-box;
}

.tab-pane {
    display: none;
    padding: 30px;
}

.tab-pane.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--border);
}

.section-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Noto Sans CJK SC', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Playlists Grid */
.playlists-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    width: 100%;
}

.playlist-card {
    background: var(--surface-hover);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 20px;
    transition: all 0.2s ease;
    width: 100%;
    box-sizing: border-box;
}

.playlist-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.playlist-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.playlist-title {
    font-weight: 600;
    font-size: 1.1rem;
}

.playlist-actions {
    display: flex;
    gap: 5px;
}

.playlist-info {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.playlist-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 8px;
}

.playlist-stats span {
    padding: 2px 8px;
    background: var(--surface-hover);
    border-radius: 12px;
    font-size: 0.8rem;
}

.playlist-duration {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: 500;
    color: var(--text-primary);
}

.playlist-duration .duration-text {
    font-family: monospace;
}

.playlist-files {
    margin: 15px 0;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--surface);
}

.playlist-file-list {
    padding: 0;
}

.playlist-file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid var(--border);
    transition: background-color 0.2s ease;
}

.playlist-file-item:last-child {
    border-bottom: none;
}

.playlist-file-item:hover {
    background-color: var(--surface-hover);
}

.playlist-file-item[data-file-path]:hover {
    background-color: #e3f2fd;
    cursor: pointer;
}

.playlist-file-item[data-file-path]:hover .file-name {
    color: #1976d2;
    text-decoration: underline;
}

.playlist-file-item.disabled {
    opacity: 0.6;
    background-color: #f8f9fa;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;
}

.file-icon {
    color: var(--text-secondary);
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.file-details {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-weight: 500;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 4px;
}

.file-meta {
    display: flex;
    gap: 15px;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.file-duration {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--text-muted);
    font-family: monospace;
    white-space: nowrap;
}

.file-type {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--text-muted);
    font-style: italic;
}

.file-status {
    margin-left: 10px;
}

.playlist-empty {
    padding: 20px;
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
}

.playlist-controls {
    display: flex;
    gap: 10px;
}

/* Schedules List */
.schedules-list {
    display: grid;
    gap: 15px;
}

.schedule-item {
    background: var(--surface-hover);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 20px;
    align-items: center;
    transition: all 0.2s ease;
}

.schedule-item.disabled {
    opacity: 0.6;
    border-color: var(--text-muted);
}

.schedule-item.enabled {
    border-left: 4px solid var(--success);
}

.schedule-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.schedule-info h4 {
    font-weight: 600;
    margin: 0;
}

.schedule-status {
    display: flex;
    align-items: center;
    gap: 5px;
}

.schedule-status .status-text.enabled {
    color: var(--success);
    font-weight: 500;
}

.schedule-status .status-text.disabled {
    color: var(--text-muted);
}

.schedule-details {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.schedule-details span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.schedule-actions {
    display: flex;
    gap: 10px;
}

/* File Browser */
.file-browser {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.browser-tabs {
    display: flex;
    border-bottom: 1px solid var(--border);
    margin-bottom: 0;
    flex-shrink: 0;
}

.browser-tab {
    padding: 12px 20px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.browser-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

/* Browser Toolbar */
.browser-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid var(--border);
    margin-bottom: 15px;
    flex-shrink: 0;
}

.navigation-controls {
    display: flex;
    gap: 5px;
}

.navigation-controls .btn {
    width: 36px;
    height: 36px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.browser-controls {
    display: flex;
    gap: 15px;
    align-items: center;
}

.search-box {
    display: flex;
    gap: 5px;
}

.search-input {
    width: 200px;
    padding: 8px 12px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.9rem;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.9rem;
    background: var(--surface);
    color: var(--text-primary);
}

.browser-pane {
    display: none;
    flex: 1;
    overflow: hidden;
    flex-direction: column;
}

.browser-pane.active {
    display: flex;
}

/* Breadcrumb */
.breadcrumb-bar {
    margin-bottom: 10px;
    flex-shrink: 0;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 0;
    font-size: 0.9rem;
}

.breadcrumb-item {
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px 8px;
    border-radius: var(--radius);
    transition: all 0.2s ease;
}

.breadcrumb-item:hover {
    background: var(--surface-hover);
    color: var(--text-primary);
}

.breadcrumb-item.active {
    color: var(--primary-color);
    font-weight: 500;
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    margin-left: 8px;
    color: var(--text-muted);
}

.path-bar {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-shrink: 0;
}

.path-input {
    flex: 1;
    padding: 10px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.9rem;
}

/* File List Container */
.file-list-container {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.file-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid var(--border);
    margin-bottom: 10px;
    flex-shrink: 0;
}

.file-count {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.view-options {
    display: flex;
    gap: 5px;
}

.view-btn {
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn.active {
    background: var(--primary-color);
    color: white;
}

.file-list {
    border: 1px solid var(--border);
    border-radius: var(--radius);
    flex: 1;
    overflow-y: auto;
    background: var(--surface);
}

.file-list.grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 10px;
    padding: 15px;
}

.file-list.grid-view .file-item {
    flex-direction: column;
    text-align: center;
    padding: 15px 10px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--surface);
    position: relative;
}

.file-list.grid-view .file-checkbox {
    position: absolute;
    top: 8px;
    left: 8px;
    margin-right: 0;
}

.file-list.grid-view .file-icon {
    font-size: 2rem;
    margin-bottom: 8px;
    margin-right: 0;
}

.file-list.grid-view .file-name {
    font-size: 0.8rem;
    word-break: break-word;
    line-height: 1.2;
}

.file-list.grid-view .file-size {
    font-size: 0.7rem;
    margin-top: 4px;
}

.file-list.grid-view .play-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid var(--border);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.file-item:hover {
    background: var(--surface-hover);
}

.file-item:last-child {
    border-bottom: none;
}

.file-item.media-file {
    background: linear-gradient(90deg, rgba(37, 99, 235, 0.05) 0%, rgba(37, 99, 235, 0.02) 100%);
    border-left: 3px solid var(--primary-color);
}

.file-item.media-file:hover {
    background: linear-gradient(90deg, rgba(37, 99, 235, 0.1) 0%, rgba(37, 99, 235, 0.05) 100%);
    cursor: pointer;
}

.file-icon {
    margin-right: 12px;
    width: 20px;
    text-align: center;
    color: var(--text-secondary);
}

.file-item.media-file .file-icon {
    color: var(--primary-color);
}

.file-name {
    flex: 1;
    font-weight: 500;
    margin: 0 15px;
    color: var(--text-primary);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Noto Sans CJK SC', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif;
    word-break: break-word;
    overflow-wrap: break-word;
    line-height: 1.4;
}

.file-size {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

.play-indicator {
    margin-left: 10px;
    color: var(--primary-color);
    font-size: 0.8rem;
    opacity: 0.7;
}

.file-item.media-file:hover .play-indicator {
    opacity: 1;
    animation: pulse 1.5s infinite;
}

/* Error Messages */
.error-message {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.error-message i {
    font-size: 3rem;
    color: var(--danger-color);
    margin-bottom: 15px;
}

.error-message h4 {
    color: var(--text-primary);
    margin-bottom: 10px;
    font-weight: 600;
}

.error-message p {
    margin-bottom: 10px;
    line-height: 1.5;
}

/* WebDAV Status Indicator */
.settings-info .status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.settings-info .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
}

.settings-info .status-dot.warning {
    background: var(--warning-color);
}

.settings-info .status-dot.error {
    background: var(--danger-color);
}

/* Enhanced File Browser Styles */
.empty-directory {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);
}

.empty-directory i {
    font-size: 4rem;
    color: var(--text-muted);
    margin-bottom: 20px;
}

.empty-directory h4 {
    color: var(--text-primary);
    margin-bottom: 10px;
    font-weight: 600;
}

.file-item.directory-item {
    background: linear-gradient(90deg, rgba(100, 116, 139, 0.05) 0%, rgba(100, 116, 139, 0.02) 100%);
}

.file-item.directory-item:hover {
    background: linear-gradient(90deg, rgba(100, 116, 139, 0.1) 0%, rgba(100, 116, 139, 0.05) 100%);
}

.file-item.directory-item .file-icon {
    color: var(--secondary-color);
}

.file-details {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 2px;
}

.file-date {
    font-size: 0.7rem;
    color: var(--text-muted);
}

/* Navigation button states */
.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .browser-toolbar {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .browser-controls {
        flex-direction: column;
        gap: 10px;
    }

    .search-input {
        width: 100%;
    }

    .breadcrumb {
        flex-wrap: wrap;
    }

    .file-list.grid-view {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
}

/* Forms */
.settings-sections {
    display: grid;
    gap: 30px;
}

.settings-section {
    border-bottom: 1px solid var(--border);
    padding-bottom: 30px;
}

.settings-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.settings-section h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--text-primary);
}

.settings-form {
    display: grid;
    gap: 20px;
}

.form-group {
    display: grid;
    gap: 8px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select {
    padding: 10px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.days-quick-select {
    display: flex;
    gap: 8px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.days-quick-select .btn {
    font-size: 0.8rem;
    padding: 4px 8px;
}

.days-of-week {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.day-checkbox {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    font-size: 0.9rem;
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: var(--surface);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid var(--border);
}

.modal-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px 30px;
    border-top: 1px solid var(--border);
}

/* Notifications */
.notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1100;
    display: grid;
    gap: 10px;
}

.notification {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 15px 20px;
    box-shadow: var(--shadow-lg);
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.error {
    border-left: 4px solid var(--danger-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
        margin: 0;
        width: 100%;
    }

    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
        padding: 15px 20px;
        margin: 0 0 20px 0;
        width: 100%;
        min-height: auto;
    }

    .header h1 {
        font-size: 1.5rem;
        min-width: auto;
        width: 100%;
        text-align: center;
        flex: none;
    }

    .header-controls {
        flex-direction: row;
        justify-content: center;
        gap: 15px;
        width: 100%;
        flex-wrap: wrap;
    }

    .language-select {
        width: auto;
        min-width: 120px;
        max-width: 200px;
    }

    .control-panel, .scheduler-panel {
        padding: 20px 15px;
        margin-bottom: 15px;
        width: 100%;
    }

    .media-controls {
        justify-content: center;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 20px;
    }

    .control-btn {
        min-width: 48px;
        min-height: 48px;
        font-size: 18px;
        width: 48px;
        height: 48px;
    }

    .tabs {
        flex-direction: column;
        gap: 5px;
        padding: 5px;
        width: 100%;
    }

    .tab-btn {
        width: 100%;
        justify-content: flex-start;
        padding: 15px 20px;
        font-size: 16px;
        min-width: auto;
    }

    .playlists-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .playlist-card {
        width: 100%;
        padding: 15px;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
        max-height: 90vh;
        overflow-y: auto;
    }

    .volume-control {
        flex-direction: column;
        gap: 10px;
        align-items: center;
        width: 100%;
    }

    .volume-slider {
        width: 100%;
        max-width: 200px;
    }

    .progress-bar-container {
        margin: 15px 0;
        width: 100%;
    }

    .time-display {
        font-size: 14px;
    }
}

/* Mobile-specific touch improvements */
@media (max-width: 480px) {
    .container {
        padding: 5px;
        width: 100%;
        margin: 0;
    }

    .header {
        padding: 10px 15px;
        width: 100%;
        margin: 0 0 15px 0;
    }

    .header h1 {
        font-size: 1.3rem;
        width: 100%;
        text-align: center;
    }

    .header-controls {
        width: 100%;
        justify-content: center;
        gap: 10px;
    }

    .control-panel, .scheduler-panel {
        padding: 15px 10px;
        margin-bottom: 10px;
        width: 100%;
    }

    .control-btn {
        min-width: 44px;
        min-height: 44px;
        font-size: 16px;
        width: 44px;
        height: 44px;
    }

    .tab-btn {
        padding: 12px 15px;
        font-size: 14px;
        width: 100%;
    }

    .btn {
        padding: 12px 16px;
        font-size: 14px;
        min-height: 44px;
        width: 100%;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 12px;
        font-size: 16px; /* Prevents zoom on iOS */
        width: 100%;
        box-sizing: border-box;
    }

    .modal-content {
        margin: 10px;
        width: calc(100% - 20px);
    }

    .playlist-card {
        padding: 10px;
        width: 100%;
    }

    .tab-content {
        width: 100%;
        min-height: 400px;
    }

    .tab-pane {
        padding: 15px;
        width: 100%;
        box-sizing: border-box;
    }
}

/* Playlist Controls in File Browser */
.playlist-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 0 15px;
    border-right: 1px solid var(--border);
    flex-wrap: wrap;
}

.playlist-select {
    min-width: 150px;
    padding: 6px 12px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--surface);
    color: var(--text-primary);
    font-size: 14px;
}

.playlist-select:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* File Selection */
.file-item.selected {
    background: linear-gradient(90deg, rgba(37, 99, 235, 0.15) 0%, rgba(37, 99, 235, 0.08) 100%) !important;
    border-left: 4px solid var(--primary-color) !important;
    color: var(--text-primary);
}

.file-item.selected .file-icon {
    color: var(--primary-color) !important;
}

.file-item.selected .file-name {
    color: var(--text-primary) !important;
    font-weight: 600;
}

.file-item.selected .file-details {
    color: var(--text-secondary) !important;
}

.file-item.selected .play-indicator {
    color: var(--primary-color) !important;
    opacity: 1;
}

.file-checkbox {
    margin-right: 12px;
    width: 18px;
    height: 18px;
    cursor: pointer;
    z-index: 2;
    opacity: 0.8;
    transition: opacity 0.2s;
    flex-shrink: 0;
}

.file-checkbox:checked {
    accent-color: var(--primary-color);
    opacity: 1;
}

.file-item:hover .file-checkbox {
    opacity: 1;
}

.file-item.media-file {
    position: relative;
    cursor: pointer;
}

.file-item.media-file:hover {
    background: linear-gradient(90deg, rgba(37, 99, 235, 0.1) 0%, rgba(37, 99, 235, 0.05) 100%);
}

/* Selected Files Preview */
.selected-files-preview {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 10px;
    background: var(--background);
}

.selected-file-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 0;
    font-size: 14px;
    color: var(--text-secondary);
}

.selected-file-item .file-icon {
    color: var(--primary-color);
}

.selected-file-item .file-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Extra small screens */
@media (max-width: 360px) {
    .header h1 {
        font-size: 1.1rem;
        line-height: 1.2;
    }

    .header-controls {
        gap: 8px;
    }

    .language-select {
        min-width: 100px;
        font-size: 13px;
        padding: 6px 10px;
    }

    .logout-btn,
    .mobile-btn {
        width: 36px;
        height: 36px;
    }

    .control-btn {
        min-width: 40px;
        min-height: 40px;
        width: 40px;
        height: 40px;
        font-size: 14px;
    }

    .container {
        padding: 3px;
    }

    .header {
        padding: 8px 12px;
    }

    .control-panel, .scheduler-panel {
        padding: 12px 8px;
    }
}
