#!/usr/bin/env python3
"""
测试APE音频文件播放的脚本
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.vlc_controller import VLCController

def test_ape_playback():
    """测试APE文件播放"""
    print("=== VLC APE音频格式支持测试 ===")
    
    # 检查APE文件是否存在
    ape_file = "data/music/任贤齐.-.[一个人任贤齐].专辑.(APE)(ED2000.COM).ape"
    if not os.path.exists(ape_file):
        print(f"错误：APE文件不存在: {ape_file}")
        return False
    
    print(f"找到APE文件: {ape_file}")
    print(f"文件大小: {os.path.getsize(ape_file) / (1024*1024):.2f} MB")
    
    # 初始化VLC控制器
    print("\n初始化VLC控制器...")
    vlc_controller = VLCController(vlc_path="C:\\Program Files\\VideoLAN\\VLC")
    
    if not vlc_controller.is_initialized:
        print("错误：VLC控制器初始化失败")
        return False
    
    print("VLC控制器初始化成功")
    
    # 加载APE文件到播放列表
    print(f"\n加载APE文件到播放列表...")
    success = vlc_controller.load_playlist([ape_file], shuffle=False, playlist_name="APE测试")
    
    if not success:
        print("错误：无法加载APE文件到播放列表")
        return False
    
    print("APE文件加载成功")
    
    # 开始播放
    print("\n开始播放APE文件...")
    success = vlc_controller.play()
    
    if not success:
        print("错误：无法开始播放")
        return False
    
    print("播放开始")
    
    # 等待一段时间让播放开始
    print("等待播放开始...")
    time.sleep(3)
    
    # 检查播放状态
    state = vlc_controller.get_state()
    print(f"当前播放状态: {state}")
    
    # 获取媒体信息
    media_info = vlc_controller.get_current_media_info()
    if media_info:
        print("\n媒体信息:")
        for key, value in media_info.items():
            if key == 'duration' and value:
                # 转换毫秒为分:秒格式
                minutes = value // 60000
                seconds = (value % 60000) // 1000
                print(f"  {key}: {minutes}:{seconds:02d}")
            elif key == 'time' and value:
                minutes = value // 60000
                seconds = (value % 60000) // 1000
                print(f"  {key}: {minutes}:{seconds:02d}")
            else:
                print(f"  {key}: {value}")
    
    # 播放几秒钟
    print(f"\n播放10秒钟进行测试...")
    for i in range(10):
        time.sleep(1)
        state = vlc_controller.get_state()
        volume = vlc_controller.get_volume()
        print(f"第{i+1}秒 - 状态: {state}, 音量: {volume}")
        
        if state.value == "error":
            print("播放出现错误")
            break
    
    # 停止播放
    print("\n停止播放...")
    vlc_controller.stop()
    
    # 清理资源
    vlc_controller.cleanup()
    
    print("\n=== APE播放测试完成 ===")
    return True

def check_vlc_codecs():
    """检查VLC支持的编解码器"""
    print("\n=== 检查VLC支持的音频格式 ===")
    
    try:
        import vlc
        
        # 创建VLC实例
        instance = vlc.Instance(['--intf', 'dummy', '--quiet'])
        
        # 获取支持的音频格式
        print("VLC版本信息:")
        print(f"  libVLC版本: {vlc.libvlc_get_version().decode()}")
        
        # 测试APE格式支持
        print("\n测试APE格式支持:")
        media = instance.media_new("test.ape")
        if media:
            print("  ✓ VLC支持APE格式")
        else:
            print("  ✗ VLC不支持APE格式")
        
        instance.release()
        
    except Exception as e:
        print(f"检查VLC编解码器时出错: {e}")

if __name__ == "__main__":
    try:
        # 检查VLC编解码器支持
        check_vlc_codecs()
        
        # 测试APE播放
        success = test_ape_playback()
        
        if success:
            print("\n✓ APE音频格式测试成功！")
        else:
            print("\n✗ APE音频格式测试失败！")
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
