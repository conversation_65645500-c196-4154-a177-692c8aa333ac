<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VLC Automation System</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1><i class="fas fa-music"></i> <span data-i18n="app_title">VLC Automation System</span></h1>
            <div class="header-controls">
                <div class="language-switcher">
                    <select id="languageSelect" class="language-select">
                        <option value="en">English</option>
                        <option value="zh">中文</option>
                    </select>
                </div>
                <div class="user-controls">
                    <a href="/mobile" class="mobile-btn" title="Mobile Version">
                        <i class="fas fa-mobile-alt"></i>
                    </a>
                    <a href="/logout" class="logout-btn" title="Logout">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
                <div class="status-indicator" id="systemStatus">
                    <span class="status-dot"></span>
                    <span class="status-text" data-i18n="status.connecting">Connecting...</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Control Panel -->
            <section class="control-panel">
                <h2><i class="fas fa-play-circle"></i> <span data-i18n="media_controls.title">Media Control</span></h2>
                <div class="media-controls">
                    <button class="control-btn" id="playBtn" data-i18n-title="media_controls.play">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="control-btn" id="pauseBtn" data-i18n-title="media_controls.pause">
                        <i class="fas fa-pause"></i>
                    </button>
                    <button class="control-btn" id="stopBtn" data-i18n-title="media_controls.stop">
                        <i class="fas fa-stop"></i>
                    </button>
                    <button class="control-btn" id="prevBtn" data-i18n-title="media_controls.previous">
                        <i class="fas fa-step-backward"></i>
                    </button>
                    <button class="control-btn" id="nextBtn" data-i18n-title="media_controls.next">
                        <i class="fas fa-step-forward"></i>
                    </button>
                    <button class="control-btn" id="repeatBtn" data-i18n-title="media_controls.repeat" title="Single Track Repeat">
                        <i class="fas fa-redo"></i>
                    </button>
                </div>

                <div class="progress-control">
                    <div class="progress-bar-container">
                        <div class="progress-bar" id="progressBar">
                            <div class="progress-fill" id="progressFill"></div>
                            <div class="progress-handle" id="progressHandle"></div>
                        </div>
                        <div class="time-display">
                            <span id="currentTime">0:00</span>
                            <span id="totalTime">0:00</span>
                        </div>
                    </div>
                </div>

                <div class="volume-control">
                    <i class="fas fa-volume-down"></i>
                    <input type="range" id="volumeSlider" min="0" max="100" value="70" class="volume-slider">
                    <i class="fas fa-volume-up"></i>
                    <span id="volumeValue">70%</span>
                </div>

                <div class="current-media" id="currentMedia">
                    <div class="media-info">
                        <div class="media-title">No media playing</div>
                        <div class="media-details"></div>
                        <div class="playlist-info" id="currentPlaylistInfo" style="display: none;">
                            <i class="fas fa-list"></i>
                            <span>Playlist: </span>
                            <span id="currentPlaylistName">None</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Scheduler Control -->
            <section class="scheduler-panel">
                <h2><i class="fas fa-clock"></i> Scheduler</h2>
                <div class="scheduler-controls">
                    <button class="btn btn-success" id="startSchedulerBtn">
                        <i class="fas fa-play"></i> Start
                    </button>
                    <button class="btn btn-warning" id="pauseSchedulerBtn">
                        <i class="fas fa-pause"></i> Pause
                    </button>
                    <button class="btn btn-danger" id="stopSchedulerBtn">
                        <i class="fas fa-stop"></i> Stop
                    </button>
                </div>
                
                <div class="scheduler-status" id="schedulerStatus">
                    <div class="status-item">
                        <span class="label">Status:</span>
                        <span class="value" id="schedulerState">Stopped</span>
                    </div>
                    <div class="status-item">
                        <span class="label">Next Schedule:</span>
                        <span class="value" id="nextSchedule">None</span>
                    </div>
                    <div class="status-item">
                        <span class="label">Current Session:</span>
                        <span class="value" id="currentSession">None</span>
                    </div>
                </div>
            </section>

            <!-- Tabs -->
            <div class="tabs">
                <button class="tab-btn active" data-tab="playlists">
                    <i class="fas fa-list"></i> <span data-i18n="navigation.playlists">Playlists</span>
                </button>
                <button class="tab-btn" data-tab="schedules">
                    <i class="fas fa-calendar"></i> <span data-i18n="navigation.schedules">Schedules</span>
                </button>
                <button class="tab-btn" data-tab="files">
                    <i class="fas fa-folder"></i> <span data-i18n="navigation.files">File Browser</span>
                </button>
                <button class="tab-btn" data-tab="settings">
                    <i class="fas fa-cog"></i> <span data-i18n="navigation.settings">Settings</span>
                </button>
            </div>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Playlists Tab -->
                <div class="tab-pane active" id="playlists">
                    <div class="section-header">
                        <h3>Playlists</h3>
                        <button class="btn btn-primary" id="addPlaylistBtn">
                            <i class="fas fa-plus"></i> Add Playlist
                        </button>
                    </div>
                    <div class="playlists-grid" id="playlistsGrid">
                        <!-- Playlists will be loaded here -->
                    </div>
                </div>

                <!-- Schedules Tab -->
                <div class="tab-pane" id="schedules">
                    <div class="section-header">
                        <h3>Schedules</h3>
                        <button class="btn btn-primary" id="addScheduleBtn">
                            <i class="fas fa-plus"></i> Add Schedule
                        </button>
                    </div>
                    <div class="schedules-list" id="schedulesList">
                        <!-- Schedules will be loaded here -->
                    </div>
                </div>

                <!-- File Browser Tab -->
                <div class="tab-pane" id="files">
                    <div class="file-browser">
                        <div class="browser-tabs">
                            <button class="browser-tab active" data-browser="local">
                                <i class="fas fa-hdd"></i> Local Files
                            </button>
                            <button class="browser-tab" data-browser="webdav">
                                <i class="fas fa-cloud"></i> WebDAV
                            </button>
                            <button class="browser-tab" data-browser="webdav2">
                                <i class="fas fa-cloud"></i> WebDAV2
                            </button>
                        </div>

                        <div class="browser-toolbar">
                            <div class="navigation-controls">
                                <button class="btn btn-sm" id="backBtn" title="Go Back">
                                    <i class="fas fa-arrow-left"></i>
                                </button>
                                <button class="btn btn-sm" id="forwardBtn" title="Go Forward">
                                    <i class="fas fa-arrow-right"></i>
                                </button>
                                <button class="btn btn-sm" id="upBtn" title="Parent Directory">
                                    <i class="fas fa-arrow-up"></i>
                                </button>
                                <button class="btn btn-sm" id="homeBtn" title="Home Directory">
                                    <i class="fas fa-home"></i>
                                </button>
                                <button class="btn btn-sm" id="refreshBtn" title="Refresh">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>

                            <div class="playlist-controls">
                                <button class="btn btn-sm btn-primary" id="createPlaylistFromSelection" title="Create Playlist from Selected Files" disabled>
                                    <i class="fas fa-plus"></i> New Playlist
                                </button>
                                <select id="addToPlaylistSelect" class="playlist-select" disabled>
                                    <option value="">Add to Playlist...</option>
                                </select>
                                <button class="btn btn-sm btn-secondary" id="addToPlaylistBtn" title="Add Selected Files to Playlist" disabled>
                                    <i class="fas fa-plus-circle"></i> Add
                                </button>
                                <button class="btn btn-sm" id="selectAllBtn" title="Select All Media Files">
                                    <i class="fas fa-check-square"></i> Select All
                                </button>
                                <button class="btn btn-sm" id="clearSelectionBtn" title="Clear Selection" disabled>
                                    <i class="fas fa-square"></i> Clear
                                </button>
                            </div>

                            <div class="browser-controls">
                                <div class="search-box">
                                    <input type="text" id="fileSearchInput" placeholder="Search files..." class="search-input">
                                    <button class="btn btn-sm" id="searchBtn">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>

                                <div class="filter-controls">
                                    <select id="fileTypeFilter" class="filter-select">
                                        <option value="all">All Files</option>
                                        <option value="media">Media Files</option>
                                        <option value="audio">Audio Only</option>
                                        <option value="video">Video Only</option>
                                        <option value="folders">Folders Only</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="browser-content">
                            <div class="browser-pane active" id="localBrowser">
                                <div class="breadcrumb-bar">
                                    <div class="breadcrumb" id="localBreadcrumb">
                                        <span class="breadcrumb-item active">Home</span>
                                    </div>
                                </div>

                                <div class="path-bar">
                                    <input type="text" id="localPath" placeholder="Enter path..." class="path-input">
                                    <button class="btn btn-sm" id="browseLocalBtn">Go</button>
                                </div>

                                <div class="file-list-container">
                                    <div class="file-list-header">
                                        <div class="file-count" id="localFileCount">0 items</div>
                                        <div class="view-options">
                                            <button class="btn btn-sm view-btn active" data-view="list" title="List View">
                                                <i class="fas fa-list"></i>
                                            </button>
                                            <button class="btn btn-sm view-btn" data-view="grid" title="Grid View">
                                                <i class="fas fa-th"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="file-list" id="localFileList">
                                        <!-- Local files will be loaded here -->
                                    </div>
                                </div>
                            </div>

                            <div class="browser-pane" id="webdavBrowser">
                                <div class="breadcrumb-bar">
                                    <div class="breadcrumb" id="webdavBreadcrumb">
                                        <span class="breadcrumb-item active">Root</span>
                                    </div>
                                </div>

                                <div class="path-bar">
                                    <input type="text" id="webdavPath" placeholder="Enter WebDAV path..." class="path-input" value="/">
                                    <button class="btn btn-sm" id="browseWebdavBtn">Go</button>
                                </div>

                                <div class="file-list-container">
                                    <div class="file-list-header">
                                        <div class="file-count" id="webdavFileCount">0 items</div>
                                        <div class="view-options">
                                            <button class="btn btn-sm view-btn active" data-view="list" title="List View">
                                                <i class="fas fa-list"></i>
                                            </button>
                                            <button class="btn btn-sm view-btn" data-view="grid" title="Grid View">
                                                <i class="fas fa-th"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="file-list" id="webdavFileList">
                                        <!-- WebDAV files will be loaded here -->
                                    </div>
                                </div>
                            </div>

                            <div class="browser-pane" id="webdav2Browser">
                                <div class="breadcrumb-bar">
                                    <div class="breadcrumb" id="webdav2Breadcrumb">
                                        <span class="breadcrumb-item active">Root</span>
                                    </div>
                                </div>

                                <div class="path-bar">
                                    <input type="text" id="webdav2Path" placeholder="Enter WebDAV2 path..." class="path-input" value="/">
                                    <button class="btn btn-sm" id="browseWebdav2Btn">Go</button>
                                </div>

                                <div class="file-list-container">
                                    <div class="file-list-header">
                                        <div class="file-count" id="webdav2FileCount">0 items</div>
                                        <div class="view-options">
                                            <button class="btn btn-sm view-btn active" data-view="list" title="List View">
                                                <i class="fas fa-list"></i>
                                            </button>
                                            <button class="btn btn-sm view-btn" data-view="grid" title="Grid View">
                                                <i class="fas fa-th"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="file-list" id="webdav2FileList">
                                        <!-- WebDAV2 files will be loaded here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Tab -->
                <div class="tab-pane" id="settings">
                    <div class="settings-sections">
                        <!-- WebDAV Settings -->
                        <div class="settings-section">
                            <h3><i class="fas fa-cloud"></i> WebDAV Configuration</h3>
                            <form id="webdavForm" class="settings-form">
                                <div class="form-group">
                                    <label for="webdavUrl">WebDAV URL:</label>
                                    <input type="url" id="webdavUrl" name="url" placeholder="https://your-webdav-server.com">
                                </div>
                                <div class="form-group">
                                    <label for="webdavUsername">Username:</label>
                                    <input type="text" id="webdavUsername" name="username">
                                </div>
                                <div class="form-group">
                                    <label for="webdavPassword">Password:</label>
                                    <input type="password" id="webdavPassword" name="password">
                                </div>
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="webdavEnabled" name="enabled">
                                        Enable WebDAV
                                    </label>
                                </div>
                                <div class="form-actions">
                                    <button type="button" class="btn btn-secondary" id="testWebdavBtn">
                                        <i class="fas fa-plug"></i> Test Connection
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Save
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- WebDAV Status -->
                        <div class="settings-section">
                            <h3><i class="fas fa-cloud"></i> WebDAV Status</h3>
                            <div class="settings-info">
                                <div class="info-item">
                                    <span class="label">Connection:</span>
                                    <span class="value" id="webdavConnectionStatus">
                                        <span class="status-indicator">
                                            <span class="status-dot" id="webdavStatusDot"></span>
                                            <span id="webdavStatusText">Unknown</span>
                                        </span>
                                    </span>
                                </div>
                                <div class="info-item">
                                    <span class="label">Server URL:</span>
                                    <span class="value" id="webdavServerUrl">Not configured</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">Username:</span>
                                    <span class="value" id="webdavUsernameDisplay">Not configured</span>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button type="button" class="btn btn-warning" id="reconnectWebdavBtn">
                                    <i class="fas fa-sync-alt"></i> Reconnect
                                </button>
                            </div>
                        </div>

                        <!-- WebDAV2 Settings -->
                        <div class="settings-section">
                            <h3><i class="fas fa-cloud"></i> WebDAV2 Configuration</h3>
                            <form id="webdav2Form" class="settings-form">
                                <div class="form-group">
                                    <label for="webdav2Url">WebDAV2 URL:</label>
                                    <input type="url" id="webdav2Url" name="url" placeholder="https://your-second-webdav-server.com">
                                </div>
                                <div class="form-group">
                                    <label for="webdav2Username">Username:</label>
                                    <input type="text" id="webdav2Username" name="username">
                                </div>
                                <div class="form-group">
                                    <label for="webdav2Password">Password:</label>
                                    <input type="password" id="webdav2Password" name="password">
                                </div>
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="webdav2Enabled" name="enabled">
                                        <span class="checkmark"></span>
                                        Enable WebDAV2
                                    </label>
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">Save WebDAV2 Settings</button>
                                    <button type="button" class="btn btn-secondary" id="testWebdav2Btn">Test Connection</button>
                                </div>
                            </form>

                            <div class="settings-info">
                                <h4>WebDAV2 Status</h4>
                                <div class="info-item">
                                    <span class="label">Connection:</span>
                                    <span class="value">
                                        <span class="status-indicator" id="webdav2Status">
                                            <i class="fas fa-circle"></i>
                                            <span class="status-text">Disconnected</span>
                                        </span>
                                    </span>
                                </div>
                                <div class="info-item">
                                    <span class="label">Server URL:</span>
                                    <span class="value" id="webdav2ServerUrl">Not configured</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">Username:</span>
                                    <span class="value" id="webdav2UsernameDisplay">Not configured</span>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button type="button" class="btn btn-warning" id="reconnectWebdav2Btn">
                                    <i class="fas fa-sync-alt"></i> Reconnect
                                </button>
                            </div>
                        </div>

                        <!-- System Settings -->
                        <div class="settings-section">
                            <h3><i class="fas fa-cog"></i> System Settings</h3>
                            <div class="settings-info">
                                <div class="info-item">
                                    <span class="label">VLC Status:</span>
                                    <span class="value" id="vlcStatus">Unknown</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">Web Port:</span>
                                    <span class="value" id="webPort">5000</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">Auto Start:</span>
                                    <span class="value" id="autoStart">Enabled</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals -->
    <!-- Playlist Modal -->
    <div class="modal" id="playlistModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="playlistModalTitle">Add Playlist</h3>
                <button class="modal-close" id="closePlaylistModal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="playlistForm">
                    <div class="form-group">
                        <label for="playlistName">Playlist Name:</label>
                        <input type="text" id="playlistName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="playlistVolume">Default Volume:</label>
                        <input type="range" id="playlistVolume" name="volume" min="0" max="100" value="70">
                        <span id="playlistVolumeValue">70%</span>
                    </div>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="playlistShuffle" name="shuffle">
                            Shuffle
                        </label>
                    </div>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="playlistRepeat" name="repeat">
                            Repeat
                        </label>
                    </div>
                    <div class="form-group">
                        <label>Playlist Items:</label>
                        <div class="playlist-items" id="playlistItems">
                            <!-- Playlist items will be added here -->
                        </div>
                        <button type="button" class="btn btn-secondary" id="addPlaylistItem">
                            <i class="fas fa-plus"></i> Add Item
                        </button>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelPlaylistBtn">Cancel</button>
                <button type="button" class="btn btn-primary" id="savePlaylistBtn">Save</button>
            </div>
        </div>
    </div>

    <!-- Quick Playlist Creation Modal -->
    <div class="modal" id="quickPlaylistModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create Playlist from Selected Files</h3>
                <button class="modal-close" id="closeQuickPlaylistModal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="quickPlaylistForm">
                    <div class="form-group">
                        <label for="quickPlaylistName">Playlist Name:</label>
                        <input type="text" id="quickPlaylistName" name="name" required placeholder="Enter playlist name...">
                    </div>
                    <div class="form-group">
                        <label for="quickPlaylistVolume">Volume:</label>
                        <input type="range" id="quickPlaylistVolume" name="volume" min="0" max="100" value="70">
                        <span id="quickPlaylistVolumeValue">70%</span>
                    </div>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="quickPlaylistShuffle" name="shuffle">
                            Shuffle
                        </label>
                    </div>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="quickPlaylistRepeat" name="repeat">
                            Repeat
                        </label>
                    </div>
                    <div class="form-group">
                        <label>Selected Files (<span id="selectedFileCount">0</span>):</label>
                        <div class="selected-files-preview" id="selectedFilesPreview">
                            <!-- Selected files will be shown here -->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelQuickPlaylistBtn">Cancel</button>
                <button type="button" class="btn btn-primary" id="createQuickPlaylistBtn">Create Playlist</button>
            </div>
        </div>
    </div>

    <!-- Schedule Modal -->
    <div class="modal" id="scheduleModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="scheduleModalTitle">Add Schedule</h3>
                <button class="modal-close" id="closeScheduleModal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="scheduleForm">
                    <div class="form-group">
                        <label for="scheduleName">Schedule Name:</label>
                        <input type="text" id="scheduleName" name="name" required>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="scheduleStartTime">Start Time:</label>
                            <input type="time" id="scheduleStartTime" name="start_time" required>
                        </div>
                        <div class="form-group">
                            <label for="scheduleEndTime">End Time:</label>
                            <input type="time" id="scheduleEndTime" name="end_time" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="schedulePlaylist">Playlist:</label>
                        <select id="schedulePlaylist" name="playlist_name" required>
                            <option value="">Select a playlist...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="scheduleVolume">Volume Override:</label>
                        <input type="range" id="scheduleVolume" name="volume" min="0" max="100" value="70">
                        <span id="scheduleVolumeValue">70%</span>
                    </div>
                    <div class="form-group">
                        <label>Days of Week:</label>
                        <div class="days-quick-select">
                            <button type="button" class="btn btn-sm btn-outline" id="selectWeekdays">Only Weekdays</button>
                            <button type="button" class="btn btn-sm btn-outline" id="selectWeekends">Only Weekends</button>
                            <button type="button" class="btn btn-sm btn-outline" id="selectAllDays">All Days</button>
                            <button type="button" class="btn btn-sm btn-outline" id="clearAllDays">Clear All</button>
                        </div>
                        <div class="days-of-week">
                            <label class="day-checkbox">
                                <input type="checkbox" name="days" value="0"> Mon
                            </label>
                            <label class="day-checkbox">
                                <input type="checkbox" name="days" value="1"> Tue
                            </label>
                            <label class="day-checkbox">
                                <input type="checkbox" name="days" value="2"> Wed
                            </label>
                            <label class="day-checkbox">
                                <input type="checkbox" name="days" value="3"> Thu
                            </label>
                            <label class="day-checkbox">
                                <input type="checkbox" name="days" value="4"> Fri
                            </label>
                            <label class="day-checkbox">
                                <input type="checkbox" name="days" value="5"> Sat
                            </label>
                            <label class="day-checkbox">
                                <input type="checkbox" name="days" value="6"> Sun
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="scheduleEnabled" name="enabled" checked>
                            Enabled
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelScheduleBtn">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveScheduleBtn">Save</button>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div class="notifications" id="notifications"></div>

    <!-- Scripts -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
