"""
VLC Media Player Controller
Handles VLC instance management, playback control, and playlist management
"""
import vlc
import os
import time
import threading
import random
from pathlib import Path
from typing import List, Optional, Callable, Dict, Any
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class PlaybackState(Enum):
    """VLC playback states"""
    STOPPED = "stopped"
    PLAYING = "playing"
    PAUSED = "paused"
    ENDED = "ended"
    ERROR = "error"

class VLCController:
    """Controls VLC media player instance"""
    
    def __init__(self, vlc_path: str = ""):
        self.vlc_path = vlc_path
        self.instance = None
        self.player = None
        self.media_list = None
        self.list_player = None
        self.current_playlist = []
        self.current_playlist_name = None
        self.current_index = 0
        self.is_initialized = False
        self.state_callbacks: List[Callable] = []
        self.volume = 70
        self.repeat_mode = False  # Single track repeat mode
        self._lock = threading.Lock()
        
        self.initialize()
    
    def initialize(self):
        """Initialize VLC instance and player"""
        try:
            # VLC instance arguments for better compatibility
            vlc_args = [
                '--intf', 'dummy',  # No interface
                '--no-video-title-show',  # Don't show video title
                '--quiet',  # Reduce console output
                '--no-osd',  # No on-screen display
            ]
            
            if self.vlc_path and os.path.exists(self.vlc_path):
                vlc_args.extend(['--plugin-path', self.vlc_path])
            
            self.instance = vlc.Instance(vlc_args)
            self.player = self.instance.media_player_new()
            self.media_list = self.instance.media_list_new()
            self.list_player = self.instance.media_list_player_new()
            
            # Set up list player
            self.list_player.set_media_player(self.player)
            self.list_player.set_media_list(self.media_list)
            
            # Set up event callbacks
            self._setup_event_callbacks()
            
            self.is_initialized = True
            logger.info("VLC Controller initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize VLC: {e}")
            self.is_initialized = False
    
    def _setup_event_callbacks(self):
        """Set up VLC event callbacks"""
        if not self.player:
            return
            
        event_manager = self.player.event_manager()
        event_manager.event_attach(vlc.EventType.MediaPlayerEndReached, self._on_end_reached)
        event_manager.event_attach(vlc.EventType.MediaPlayerEncounteredError, self._on_error)
        event_manager.event_attach(vlc.EventType.MediaPlayerPlaying, self._on_playing)
        event_manager.event_attach(vlc.EventType.MediaPlayerPaused, self._on_paused)
        event_manager.event_attach(vlc.EventType.MediaPlayerStopped, self._on_stopped)
    
    def _on_end_reached(self, event):
        """Handle end of media reached"""
        logger.info("Media playback ended")

        # If repeat mode is enabled, restart the current track
        if self.repeat_mode:
            logger.info("Repeat mode enabled, restarting current track")
            try:
                # Use a timer to restart after a short delay to avoid conflicts
                import threading
                def restart_track():
                    try:
                        # Get current media
                        current_media = self.player.get_media()
                        if current_media:
                            # Stop current playback
                            self.player.stop()
                            # Wait a moment for stop to complete
                            import time
                            time.sleep(0.2)
                            # Set the same media again and play
                            self.player.set_media(current_media)
                            self.player.play()
                            logger.info("Track restarted successfully in repeat mode")
                        else:
                            logger.warning("No current media found for repeat")
                    except Exception as e:
                        logger.error(f"Error in delayed restart: {e}")

                # Start restart in a separate thread after a short delay
                restart_timer = threading.Timer(0.5, restart_track)
                restart_timer.start()
                return
            except Exception as e:
                logger.error(f"Error setting up track restart in repeat mode: {e}")

        self._notify_state_change(PlaybackState.ENDED)
    
    def _on_error(self, event):
        """Handle playback error"""
        error_details = self._get_detailed_error_info()
        logger.error(f"VLC playback error occurred: {error_details}")
        self._notify_state_change(PlaybackState.ERROR)
    
    def _on_playing(self, event):
        """Handle playback started"""
        logger.info("VLC playback started")
        self._notify_state_change(PlaybackState.PLAYING)
    
    def _on_paused(self, event):
        """Handle playback paused"""
        logger.info("VLC playback paused")
        self._notify_state_change(PlaybackState.PAUSED)
    
    def _on_stopped(self, event):
        """Handle playback stopped"""
        logger.info("VLC playback stopped")
        self._notify_state_change(PlaybackState.STOPPED)
    
    def _get_detailed_error_info(self) -> str:
        """Get detailed information about VLC errors"""
        try:
            if not self.is_initialized or not self.player:
                return "VLC not initialized"

            # Get current media info
            media = self.player.get_media()
            if media:
                media_path = media.get_mrl()
                media_state = self.player.get_state()

                # Check if it's a network stream
                if media_path and media_path.startswith(('http://', 'https://', 'ftp://')):
                    # Test if URL is accessible
                    url_status = self._test_url_accessibility(media_path)
                    return f"Network stream error - URL: {media_path}, State: {media_state}, URL Test: {url_status}"
                else:
                    # Check if local file exists
                    if media_path and media_path.startswith('file://'):
                        file_path = media_path[7:]  # Remove 'file://' prefix
                        if not os.path.exists(file_path):
                            return f"File not found: {file_path}"
                        else:
                            return f"File access error: {file_path}, State: {media_state}"
                    else:
                        return f"Media error - Path: {media_path}, State: {media_state}"
            else:
                return "No media loaded"

        except Exception as e:
            return f"Error getting details: {e}"

    def _test_url_accessibility(self, url: str) -> str:
        """Test if a URL is accessible via HTTP HEAD request"""
        try:
            import requests
            from urllib.parse import urlparse

            # Parse URL to extract credentials
            parsed = urlparse(url)
            if parsed.username and parsed.password:
                # Remove credentials from URL for the request
                clean_url = f"{parsed.scheme}://{parsed.hostname}"
                if parsed.port:
                    clean_url += f":{parsed.port}"
                clean_url += parsed.path
                if parsed.query:
                    clean_url += f"?{parsed.query}"

                # Make request with auth
                response = requests.head(clean_url,
                                       auth=(parsed.username, parsed.password),
                                       timeout=10,
                                       allow_redirects=True)
                return f"HTTP {response.status_code}"
            else:
                # No credentials, test directly
                response = requests.head(url, timeout=10, allow_redirects=True)
                return f"HTTP {response.status_code}"

        except requests.exceptions.Timeout:
            return "Timeout"
        except requests.exceptions.ConnectionError:
            return "Connection Error"
        except requests.exceptions.HTTPError as e:
            return f"HTTP Error: {e}"
        except Exception as e:
            return f"Test Error: {e}"

    def _notify_state_change(self, state: PlaybackState):
        """Notify registered callbacks of state changes"""
        for callback in self.state_callbacks:
            try:
                callback(state)
            except Exception as e:
                logger.error(f"Error in state callback: {e}")
    
    def add_state_callback(self, callback: Callable):
        """Add a callback for state changes"""
        self.state_callbacks.append(callback)
    
    def remove_state_callback(self, callback: Callable):
        """Remove a state change callback"""
        if callback in self.state_callbacks:
            self.state_callbacks.remove(callback)
    
    def load_playlist(self, file_paths: List[str], shuffle: bool = False, playlist_name: Optional[str] = None):
        """Load a playlist of media files"""
        if not self.is_initialized:
            logger.error("VLC not initialized")
            return False

        with self._lock:
            try:
                # Clear existing playlist
                self.media_list.release()
                self.media_list = self.instance.media_list_new()
                self.list_player.set_media_list(self.media_list)

                # Shuffle file paths if requested
                if shuffle:
                    file_paths = file_paths.copy()  # Don't modify the original list
                    random.shuffle(file_paths)

                # Add media files
                valid_files = []
                for file_path in file_paths:
                    try:
                        if os.path.exists(file_path) or file_path.startswith(('http://', 'https://', 'ftp://')):
                            media = self.instance.media_new(file_path)
                            if media:
                                self.media_list.add_media(media)
                                valid_files.append(file_path)
                                logger.debug(f"Added to playlist: {file_path}")
                            else:
                                logger.warning(f"Failed to create media for: {file_path}")
                        else:
                            logger.warning(f"File not found: {file_path}")
                    except Exception as e:
                        logger.error(f"Error processing file {file_path}: {e}")

                self.current_playlist = valid_files
                self.current_playlist_name = playlist_name
                self.current_index = 0

                # Set playback mode to default (no need for random mode since we shuffle the list)
                self.list_player.set_playback_mode(vlc.PlaybackMode.default)

                logger.info(f"Loaded playlist with {len(valid_files)} files{' (shuffled)' if shuffle else ''}")
                return len(valid_files) > 0

            except Exception as e:
                logger.error(f"Error loading playlist: {e}")
                return False
    
    def play(self):
        """Start playback"""
        if not self.is_initialized:
            logger.warning("Cannot play: VLC not initialized")
            return False

        if not self.current_playlist:
            logger.warning("Cannot play: No playlist loaded")
            return False

        try:
            logger.info(f"Starting playback of playlist with {len(self.current_playlist)} files")
            logger.debug(f"First file in playlist: {self.current_playlist[0] if self.current_playlist else 'None'}")

            self.list_player.play()
            self.set_volume(self.volume)

            # Give VLC a moment to start, then check state
            time.sleep(0.1)
            state = self.get_state()
            logger.debug(f"VLC state after play command: {state}")

            return True
        except Exception as e:
            logger.error(f"Error starting playback: {e}")
            return False
    
    def pause(self):
        """Pause playback"""
        if not self.is_initialized:
            return False
        
        try:
            self.list_player.pause()
            return True
        except Exception as e:
            logger.error(f"Error pausing playback: {e}")
            return False
    
    def stop(self):
        """Stop playback"""
        if not self.is_initialized:
            return False
        
        try:
            self.list_player.stop()
            return True
        except Exception as e:
            logger.error(f"Error stopping playback: {e}")
            return False
    
    def next_track(self):
        """Skip to next track"""
        if not self.is_initialized:
            return False
        
        try:
            self.list_player.next()
            return True
        except Exception as e:
            logger.error(f"Error skipping to next track: {e}")
            return False
    
    def previous_track(self):
        """Skip to previous track"""
        if not self.is_initialized:
            return False
        
        try:
            self.list_player.previous()
            return True
        except Exception as e:
            logger.error(f"Error skipping to previous track: {e}")
            return False
    
    def set_volume(self, volume: int):
        """Set playback volume (0-100)"""
        if not self.is_initialized:
            return False
        
        try:
            volume = max(0, min(100, volume))  # Clamp to 0-100
            self.player.audio_set_volume(volume)
            self.volume = volume
            logger.debug(f"Volume set to {volume}")
            return True
        except Exception as e:
            logger.error(f"Error setting volume: {e}")
            return False
    
    def get_volume(self) -> int:
        """Get current volume"""
        if not self.is_initialized:
            return 0

        try:
            return self.player.audio_get_volume()
        except Exception as e:
            logger.error(f"Error getting volume: {e}")
            return self.volume

    def set_repeat_mode(self, enabled: bool):
        """Set single track repeat mode"""
        self.repeat_mode = enabled
        logger.info(f"Repeat mode {'enabled' if enabled else 'disabled'}")

    def get_repeat_mode(self) -> bool:
        """Get current repeat mode status"""
        return self.repeat_mode

    def toggle_repeat_mode(self) -> bool:
        """Toggle repeat mode and return new state"""
        self.repeat_mode = not self.repeat_mode
        logger.info(f"Repeat mode toggled to {'enabled' if self.repeat_mode else 'disabled'}")
        return self.repeat_mode

    def seek_to_percentage(self, percentage: float) -> bool:
        """Seek to a specific percentage of the media duration"""
        if not self.is_initialized:
            logger.warning("Cannot seek: VLC not initialized")
            return False

        try:
            # Clamp percentage to valid range
            percentage = max(0.0, min(100.0, percentage))
            # Convert percentage to position (0.0 to 1.0)
            position = percentage / 100.0

            self.player.set_position(position)
            logger.info(f"Seeked to {percentage:.1f}% ({position:.3f})")
            return True
        except Exception as e:
            logger.error(f"Error seeking to {percentage}%: {e}")
            return False

    def get_state(self) -> PlaybackState:
        """Get current playback state"""
        if not self.is_initialized:
            return PlaybackState.ERROR
        
        try:
            vlc_state = self.player.get_state()
            state_mapping = {
                vlc.State.NothingSpecial: PlaybackState.STOPPED,
                vlc.State.Opening: PlaybackState.STOPPED,
                vlc.State.Buffering: PlaybackState.STOPPED,
                vlc.State.Playing: PlaybackState.PLAYING,
                vlc.State.Paused: PlaybackState.PAUSED,
                vlc.State.Stopped: PlaybackState.STOPPED,
                vlc.State.Ended: PlaybackState.ENDED,
                vlc.State.Error: PlaybackState.ERROR,
            }
            return state_mapping.get(vlc_state, PlaybackState.ERROR)
        except Exception as e:
            logger.error(f"Error getting state: {e}")
            return PlaybackState.ERROR
    
    def get_current_media_info(self) -> Dict[str, Any]:
        """Get information about currently playing media"""
        if not self.is_initialized or not self.player:
            return {}
        
        try:
            media = self.player.get_media()
            if not media:
                return {}
            
            info = {
                'title': media.get_meta(vlc.Meta.Title) or 'Unknown',
                'artist': media.get_meta(vlc.Meta.Artist) or 'Unknown',
                'album': media.get_meta(vlc.Meta.Album) or 'Unknown',
                'duration': media.get_duration(),
                'position': self.player.get_position(),
                'time': self.player.get_time(),
                'length': self.player.get_length(),
                'mrl': media.get_mrl()
            }
            return info
        except Exception as e:
            logger.error(f"Error getting media info: {e}")
            return {}
    
    def cleanup(self):
        """Clean up VLC resources"""
        try:
            if self.list_player:
                self.list_player.stop()
                self.list_player.release()
            
            if self.player:
                self.player.stop()
                self.player.release()
            
            if self.media_list:
                self.media_list.release()
            
            if self.instance:
                self.instance.release()
            
            logger.info("VLC Controller cleaned up")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    def __del__(self):
        """Destructor to ensure cleanup"""
        self.cleanup()
