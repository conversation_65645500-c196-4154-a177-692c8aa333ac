/* VLC Automation System - Mobile Styles */

:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --background: #f8fafc;
    --surface: #ffffff;
    --surface-hover: #f1f5f9;
    --border: #e2e8f0;
    --border-light: #f1f5f9;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --radius: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    
    /* Mobile specific variables */
    --header-height: 0px; /* No fixed header now */
    --bottom-nav-height: 80px;
    --safe-area-top: env(safe-area-inset-top);
    --safe-area-bottom: env(safe-area-inset-bottom);
    --touch-target: 48px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Noto Sans CJK SC', 'Microsoft YaHei', '微软雅黑', sans-serif;
    background-color: var(--background);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    padding-top: var(--safe-area-top);
    padding-bottom: var(--safe-area-bottom);
    min-height: 100vh;
    min-height: 100dvh;
}

/* Mobile Header (fully scrollable) */
.mobile-header {
    position: relative;
    background: var(--surface);
    border-bottom: 1px solid var(--border);
    padding-top: var(--safe-area-top);
}

.header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    min-height: 60px;
}

.app-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
}

.app-title i {
    font-size: 1.5rem;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.header-btn {
    width: var(--touch-target);
    height: var(--touch-target);
    border: none;
    background: var(--surface-hover);
    border-radius: var(--radius);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.header-btn:hover,
.header-btn:active {
    background: var(--border);
    color: var(--text-primary);
    transform: scale(0.95);
}

.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background: var(--surface-hover);
    border-top: 1px solid var(--border-light);
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Current Media Section */
.current-media-section {
    margin-top: var(--header-height);
    padding: 24px 20px;
    background: var(--surface);
    border-bottom: 1px solid var(--border);
    position: relative;
    z-index: 1;
}

.media-artwork {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.artwork-placeholder {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    box-shadow: var(--shadow-lg);
}

.media-info {
    text-align: center;
}

.media-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
    line-height: 1.4;
}

.media-details {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 12px;
}

.playlist-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 0.875rem;
    color: var(--text-muted);
}

/* Fixed Media Player */
.fixed-media-player {
    position: fixed;
    bottom: var(--bottom-nav-height);
    left: 0;
    right: 0;
    background: var(--surface);
    border-top: 1px solid var(--border);
    z-index: 1001;
    box-shadow: var(--shadow-lg);
    padding: 12px 16px;
    padding-bottom: calc(12px + var(--safe-area-bottom));
}

/* Now Playing Info */
.now-playing-info {
    margin-bottom: 8px;
}

.media-title-compact {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 6px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.progress-container-compact {
    display: flex;
    align-items: center;
    gap: 8px;
}

.progress-bar-compact {
    flex: 1;
    height: 4px;
    background: var(--border);
    border-radius: 2px;
    position: relative;
    cursor: pointer;
    overflow: hidden;
}

.progress-fill-compact {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: 2px;
    width: 0%;
    transition: width 0.1s ease;
}

.progress-time-compact {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-variant-numeric: tabular-nums;
    white-space: nowrap;
}

/* Media Controls Compact */
.media-controls-compact {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
}

.control-btn-compact {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    background: var(--surface-hover);
    color: var(--text-primary);
    box-shadow: var(--shadow);
}

.control-btn-compact.primary {
    width: 44px;
    height: 44px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    font-size: 1rem;
}

.control-btn-compact:hover,
.control-btn-compact:active {
    transform: scale(0.95);
}

.control-btn-compact.primary:hover,
.control-btn-compact.primary:active {
    background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
}

.control-btn-compact.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: var(--shadow-lg);
}

/* Volume Slider Overlay */
.volume-slider-overlay {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius) var(--radius) 0 0;
    padding: 16px;
    box-shadow: var(--shadow-lg);
}

.volume-slider-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.volume-slider-compact {
    flex: 1;
    height: 4px;
    border-radius: 2px;
    background: var(--border);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.volume-slider-compact::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    box-shadow: var(--shadow);
}

.volume-slider-compact::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
    box-shadow: var(--shadow);
}

.volume-value-compact {
    font-size: 0.75rem;
    color: var(--text-secondary);
    min-width: 35px;
    text-align: right;
    font-variant-numeric: tabular-nums;
}

.time-display {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-variant-numeric: tabular-nums;
}

.progress-container {
    position: relative;
}

.progress-bar {
    height: 6px;
    background: var(--border);
    border-radius: 3px;
    position: relative;
    cursor: pointer;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: 3px;
    width: 0%;
    transition: width 0.1s ease;
}

.progress-handle {
    position: absolute;
    top: 50%;
    left: 0%;
    width: 20px;
    height: 20px;
    background: var(--surface);
    border: 3px solid var(--primary-color);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    cursor: grab;
    opacity: 0;
    transition: opacity 0.2s ease;
    box-shadow: var(--shadow);
}

.progress-bar:active .progress-handle,
.progress-bar:hover .progress-handle {
    opacity: 1;
}

.progress-handle:active {
    cursor: grabbing;
    transform: translate(-50%, -50%) scale(1.2);
}

/* Legacy Media Controls (removed) */

.main-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
}

.control-btn {
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1.2rem;
    box-shadow: var(--shadow);
}

.control-btn.small {
    width: 40px;
    height: 40px;
    font-size: 1rem;
}

.control-btn.secondary {
    width: 56px;
    height: 56px;
    background: var(--surface-hover);
    color: var(--text-primary);
}

.control-btn.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
}

.control-btn.primary.large {
    width: 72px;
    height: 72px;
    font-size: 1.5rem;
}

.control-btn:hover,
.control-btn:active {
    transform: scale(0.95);
}

.control-btn.primary:hover,
.control-btn.primary:active {
    background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
}

.secondary-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 12px;
}

.volume-btn {
    width: var(--touch-target);
    height: var(--touch-target);
    border: none;
    background: var(--surface-hover);
    border-radius: var(--radius);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.volume-btn:hover,
.volume-btn:active {
    background: var(--border);
    color: var(--text-primary);
}

.volume-slider-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.volume-slider {
    width: 100px;
    height: 6px;
    border-radius: 3px;
    background: var(--border);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    box-shadow: var(--shadow);
}

.volume-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
    box-shadow: var(--shadow);
}

#volumeValue {
    font-size: 0.875rem;
    color: var(--text-secondary);
    min-width: 35px;
    text-align: right;
    font-variant-numeric: tabular-nums;
}

/* Scheduler Section */
.scheduler-section {
    padding: 20px;
    background: var(--surface);
    border-bottom: 1px solid var(--border);
    margin-bottom: 20px; /* Add space above fixed controls */
}

.scheduler-status-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: var(--background);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.875rem;
}

.scheduler-status-badge .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--danger-color);
}

.scheduler-status-badge .status-text {
    font-weight: 500;
    color: var(--text-primary);
}

.scheduler-controls {
    display: flex;
    gap: 12px;
    margin: 16px 0;
    justify-content: center;
}

.scheduler-controls .btn {
    flex: 1;
    max-width: 100px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.section-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.scheduler-controls {
    display: flex;
    gap: 8px;
}

.scheduler-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
}

.info-item .label {
    color: var(--text-secondary);
    font-weight: 500;
}

.info-item .value {
    color: var(--text-primary);
    font-weight: 500;
    text-align: right;
}

/* Bottom Navigation */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--surface);
    border-top: 1px solid var(--border);
    display: flex;
    z-index: 1000;
    padding-bottom: var(--safe-area-bottom);
}

.nav-btn {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px 8px;
    border: none;
    background: transparent;
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: var(--bottom-nav-height);
    gap: 4px;
}

.nav-btn.active {
    color: var(--primary-color);
    background: var(--surface-hover);
}

.nav-btn i {
    font-size: 1.2rem;
}

.nav-btn span {
    font-size: 0.75rem;
    font-weight: 500;
}

.nav-btn:hover,
.nav-btn:active {
    background: var(--surface-hover);
    transform: scale(0.98);
}

/* Tab Container */
.tab-container {
    margin-bottom: calc(var(--bottom-nav-height) + 100px); /* Space for compact fixed controls */
    min-height: calc(100vh - var(--header-height) - var(--bottom-nav-height) - 100px);
    min-height: calc(100dvh - var(--header-height) - var(--bottom-nav-height) - 100px);
}

.tab-content {
    display: none;
    height: 100%;
}

.tab-content.active {
    display: flex;
    flex-direction: column;
}

.tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: var(--surface);
    border-bottom: 1px solid var(--border);
    position: sticky;
    top: var(--header-height);
    z-index: 100;
}

.tab-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.content-scroll {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

/* Buttons */
.btn {
    border: none;
    border-radius: var(--radius);
    padding: 12px 20px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-height: var(--touch-target);
    text-decoration: none;
}

.btn.small {
    padding: 8px 12px;
    font-size: 0.8rem;
    min-height: 36px;
}

.btn.large {
    padding: 16px 24px;
    font-size: 1rem;
    min-height: 56px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: var(--shadow);
}

.btn-primary:hover,
.btn-primary:active {
    background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--surface-hover);
    color: var(--text-primary);
    border: 1px solid var(--border);
}

.btn-secondary:hover,
.btn-secondary:active {
    background: var(--border);
    transform: translateY(-1px);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover,
.btn-success:active {
    background: #059669;
    transform: translateY(-1px);
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover,
.btn-warning:active {
    background: #d97706;
    transform: translateY(-1px);
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover,
.btn-danger:active {
    background: #dc2626;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border);
}

.btn-outline:hover,
.btn-outline:active {
    background: var(--surface-hover);
    color: var(--text-primary);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* File Actions */
.file-actions {
    display: flex;
    gap: 8px;
}

/* Browser Tabs */
.browser-tabs {
    display: flex;
    background: var(--surface);
    border-bottom: 1px solid var(--border);
}

.browser-tab {
    flex: 1;
    padding: 16px 12px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}

.browser-tab.active {
    color: var(--primary-color);
    background: var(--surface-hover);
    border-bottom: 2px solid var(--primary-color);
}

.browser-tab i {
    font-size: 1.1rem;
}

/* Browser Content */
.browser-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.nav-bar {
    padding: 16px 20px;
    background: var(--surface);
    border-bottom: 1px solid var(--border);
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.nav-controls {
    display: flex;
    gap: 8px;
}

.nav-btn {
    width: var(--touch-target);
    height: var(--touch-target);
    border: none;
    background: var(--surface-hover);
    border-radius: var(--radius);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.nav-btn:hover,
.nav-btn:active {
    background: var(--border);
    color: var(--text-primary);
    transform: scale(0.95);
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.path-display {
    background: var(--surface-hover);
    border-radius: var(--radius);
    padding: 12px 16px;
    font-size: 0.875rem;
    color: var(--text-secondary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Search and Filter */
.search-filter-bar {
    padding: 16px 20px;
    background: var(--surface);
    border-bottom: 1px solid var(--border);
    display: flex;
    gap: 12px;
}

.search-box {
    flex: 1;
    display: flex;
    background: var(--surface-hover);
    border-radius: var(--radius);
    overflow: hidden;
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 12px 16px;
    font-size: 0.875rem;
    color: var(--text-primary);
    outline: none;
}

.search-input::placeholder {
    color: var(--text-muted);
}

.search-btn {
    width: var(--touch-target);
    height: var(--touch-target);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.search-btn:hover,
.search-btn:active {
    color: var(--primary-color);
    background: var(--border-light);
}

.filter-select {
    padding: 12px 16px;
    border: none;
    background: var(--surface-hover);
    border-radius: var(--radius);
    color: var(--text-primary);
    font-size: 0.875rem;
    cursor: pointer;
    outline: none;
    min-width: 100px;
}

/* File List */
.file-list {
    padding: 0 20px 20px;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid var(--border-light);
    cursor: pointer;
    transition: all 0.2s ease;
    gap: 16px;
}

.file-item:last-child {
    border-bottom: none;
}

.file-item:hover,
.file-item:active {
    background: var(--surface-hover);
    margin: 0 -20px;
    padding-left: 20px;
    padding-right: 20px;
}

.file-item.selected {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
    margin: 0 -20px;
    padding-left: 20px;
    padding-right: 20px;
}

.file-checkbox {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.file-item.selected .file-checkbox {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.file-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.file-icon.folder {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: white;
}

.file-icon.audio {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.file-icon.video {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

.file-icon.other {
    background: var(--surface-hover);
    color: var(--text-secondary);
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.file-details {
    font-size: 0.75rem;
    color: var(--text-muted);
    display: flex;
    gap: 12px;
}

.file-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: auto;
}

.file-play-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    box-shadow: var(--shadow);
}

.file-play-btn:hover,
.file-play-btn:active {
    background: var(--primary-hover);
    transform: scale(0.95);
}

/* Selection Actions */
.selection-actions {
    position: fixed;
    bottom: var(--bottom-nav-height);
    left: 0;
    right: 0;
    background: var(--surface);
    border-top: 1px solid var(--border);
    padding: 16px 20px;
    z-index: 999;
    box-shadow: var(--shadow-lg);
}

.selection-info {
    margin-bottom: 12px;
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-align: center;
}

.action-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
}

.playlist-select {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid var(--border);
    background: var(--surface);
    border-radius: var(--radius);
    color: var(--text-primary);
    font-size: 0.875rem;
    cursor: pointer;
    outline: none;
}

/* Mobile Modals */
.mobile-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2000;
    display: none;
    align-items: flex-end;
    justify-content: center;
}

.mobile-modal.show {
    display: flex;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    background: var(--surface);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    max-height: 90vh;
    width: 100%;
    max-width: 500px;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    animation: slideUp 0.3s ease-out;
    margin-bottom: var(--safe-area-bottom);
}

.modal-content.large {
    max-height: 95vh;
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border);
    flex-shrink: 0;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    width: var(--touch-target);
    height: var(--touch-target);
    border: none;
    background: var(--surface-hover);
    border-radius: var(--radius);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1.2rem;
}

.modal-close:hover,
.modal-close:active {
    background: var(--border);
    color: var(--text-primary);
    transform: scale(0.95);
}

.modal-body {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding: 20px;
}

.modal-footer {
    display: flex;
    gap: 12px;
    padding: 20px;
    border-top: 1px solid var(--border);
    flex-shrink: 0;
}

.modal-footer .btn {
    flex: 1;
}

/* Language Options */
.language-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.language-option {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--surface);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
    font-weight: 500;
}

.language-option:hover,
.language-option:active {
    background: var(--surface-hover);
    border-color: var(--primary-color);
    transform: scale(0.98);
}

.language-option .flag {
    font-size: 1.5rem;
}

.language-option .name {
    color: var(--text-primary);
}

/* Settings Tabs */
.settings-tabs {
    display: flex;
    border-bottom: 1px solid var(--border);
    margin-bottom: 20px;
}

.settings-tab {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
    border-bottom: 2px solid transparent;
}

.settings-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.settings-content {
    position: relative;
}

.settings-pane {
    display: none;
}

.settings-pane.active {
    display: block;
}

/* Mobile Forms */
.mobile-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 16px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--surface);
    color: var(--text-primary);
    font-size: 0.875rem;
    outline: none;
    transition: all 0.2s ease;
    min-height: var(--touch-target);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-row {
    display: flex;
    gap: 12px;
}

.form-row .form-group {
    flex: 1;
}

.form-actions {
    display: flex;
    gap: 12px;
    margin-top: 8px;
}

.form-actions .btn {
    flex: 1;
}

/* Switch Controls */
.switch-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.switch-label input[type="checkbox"] {
    display: none;
}

.switch-slider {
    position: relative;
    width: 48px;
    height: 28px;
    background: var(--border);
    border-radius: 14px;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.switch-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 24px;
    height: 24px;
    background: white;
    border-radius: 50%;
    transition: all 0.2s ease;
    box-shadow: var(--shadow);
}

.switch-label input[type="checkbox"]:checked + .switch-slider {
    background: var(--primary-color);
}

.switch-label input[type="checkbox"]:checked + .switch-slider::before {
    transform: translateX(20px);
}

/* Range Sliders */
.range-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--border);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    margin: 8px 0;
}

.range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    box-shadow: var(--shadow);
    transition: all 0.2s ease;
}

.range-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.range-slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
    box-shadow: var(--shadow);
    transition: all 0.2s ease;
}

.range-slider::-moz-range-thumb:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

/* Days of Week */
.days-quick-select {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
    flex-wrap: wrap;
}

.days-of-week {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
}

.day-checkbox {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.day-checkbox input[type="checkbox"] {
    display: none;
}

.day-label {
    width: 40px;
    height: 40px;
    border: 2px solid var(--border);
    border-radius: var(--radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-secondary);
    transition: all 0.2s ease;
}

.day-checkbox input[type="checkbox"]:checked + .day-label {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Connection Status */
.connection-status {
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid var(--border);
}

.connection-status h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.status-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.status-info .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
}

.status-info .label {
    color: var(--text-secondary);
    font-weight: 500;
}

.status-info .value {
    color: var(--text-primary);
    font-weight: 500;
    text-align: right;
    max-width: 60%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Playlists Grid */
.playlists-grid {
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
}

.playlist-card {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: var(--shadow);
}

.playlist-card:hover,
.playlist-card:active {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.playlist-items {
    margin-top: 16px;
    border-top: 1px solid var(--border);
    padding-top: 16px;
}

.playlist-item {
    display: flex;
    align-items: center;
    padding: 12px;
    margin-bottom: 8px;
    background: var(--background);
    border-radius: var(--radius);
    border: 1px solid var(--border);
    cursor: pointer;
    transition: all 0.2s ease;
}

.playlist-item:hover {
    background: var(--surface-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.playlist-item-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    margin-right: 12px;
    font-size: 0.875rem;
}

.playlist-item-info {
    flex: 1;
    min-width: 0;
}

.playlist-item-name {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.playlist-item-path {
    font-size: 0.75rem;
    color: var(--text-muted);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.playlist-item-actions {
    margin-left: 12px;
}

.playlist-empty {
    text-align: center;
    padding: 20px;
    color: var(--text-muted);
    font-style: italic;
}

.expand-btn {
    margin-left: 8px;
}

.playlist-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.playlist-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.playlist-info {
    font-size: 0.875rem;
    color: var(--text-secondary);
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.playlist-actions {
    display: flex;
    gap: 8px;
}

.playlist-actions .btn {
    padding: 8px;
    min-height: 36px;
    font-size: 0.875rem;
}

/* Schedules List */
.schedules-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.schedule-card {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: 20px;
    box-shadow: var(--shadow);
}

.schedule-card.disabled {
    opacity: 0.6;
}

.schedule-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.schedule-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.schedule-time {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.schedule-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
}

.schedule-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
}

.schedule-detail .label {
    color: var(--text-secondary);
}

.schedule-detail .value {
    color: var(--text-primary);
    font-weight: 500;
}

.schedule-days {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.schedule-day {
    padding: 4px 8px;
    background: var(--surface-hover);
    border-radius: 4px;
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.schedule-day.active {
    background: var(--primary-color);
    color: white;
}

.schedule-actions {
    display: flex;
    gap: 8px;
}

.schedule-actions .btn {
    flex: 1;
    padding: 8px;
    min-height: 36px;
    font-size: 0.875rem;
}

/* Mobile Notifications */
.mobile-notifications {
    position: fixed;
    top: calc(var(--header-height) + var(--safe-area-top) + 20px);
    left: 20px;
    right: 20px;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: 12px;
    pointer-events: none;
}

.notification {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: 16px 20px;
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 12px;
    animation: slideInRight 0.3s ease-out;
    pointer-events: auto;
    max-width: 100%;
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.error {
    border-left: 4px solid var(--danger-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification.info {
    border-left: 4px solid var(--primary-color);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.notification.success .notification-icon {
    color: var(--success-color);
}

.notification.error .notification-icon {
    color: var(--danger-color);
}

.notification.warning .notification-icon {
    color: var(--warning-color);
}

.notification.info .notification-icon {
    color: var(--primary-color);
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.notification-message {
    font-size: 0.8rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.notification-close {
    width: 24px;
    height: 24px;
    border: none;
    background: transparent;
    color: var(--text-muted);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.notification-close:hover,
.notification-close:active {
    background: var(--surface-hover);
    color: var(--text-primary);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 4000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner {
    background: var(--surface);
    border-radius: var(--radius-lg);
    padding: 32px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    box-shadow: var(--shadow-lg);
}

.loading-spinner i {
    font-size: 2rem;
    color: var(--primary-color);
}

.loading-spinner span {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 480px) {
    :root {
        --header-height: 0px; /* No fixed header now */
        --bottom-nav-height: 70px;
    }

    .tab-container {
        margin-bottom: calc(var(--bottom-nav-height) + 90px); /* Compact space on mobile */
    }

    .fixed-media-player {
        padding: 10px 12px;
    }

    .media-controls-compact {
        gap: 8px;
    }

    .control-btn-compact {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }

    .control-btn-compact.primary {
        width: 40px;
        height: 40px;
        font-size: 0.9rem;
    }
}

    .header-top {
        padding: 12px 16px;
        min-height: 50px;
    }

    .app-title {
        font-size: 1.1rem;
    }

    .app-title i {
        font-size: 1.3rem;
    }

    .status-bar {
        padding: 10px 16px;
    }

    .current-media-section {
        padding: 20px 16px;
    }

    .artwork-placeholder {
        width: 100px;
        height: 100px;
        font-size: 2.5rem;
    }

    .media-title {
        font-size: 1.1rem;
    }

    .progress-section {
        padding: 16px;
    }

    .media-controls-section {
        padding: 20px 16px;
    }

    .control-btn.primary.large {
        width: 64px;
        height: 64px;
        font-size: 1.3rem;
    }

    .control-btn.secondary {
        width: 48px;
        height: 48px;
    }

    .scheduler-section {
        padding: 16px;
    }

    .tab-header {
        padding: 16px;
    }

    .nav-bar {
        padding: 12px 16px;
    }

    .search-filter-bar {
        padding: 12px 16px;
    }

    .file-list {
        padding: 0 16px 16px;
    }

    .selection-actions {
        padding: 12px 16px;
    }

    .modal-body {
        padding: 16px;
    }

    .modal-header,
    .modal-footer {
        padding: 16px;
    }

    .playlists-grid,
    .schedules-list {
        padding: 16px;
    }

    .mobile-notifications {
        left: 16px;
        right: 16px;
    }
}

@media (max-width: 360px) {
    .form-row {
        flex-direction: column;
    }

    .days-quick-select {
        flex-direction: column;
    }

    .days-quick-select .btn {
        width: 100%;
    }

    .action-buttons {
        flex-direction: column;
    }

    .volume-slider {
        width: 80px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --background: #0f172a;
        --surface: #1e293b;
        --surface-hover: #334155;
        --border: #475569;
        --border-light: #334155;
        --text-primary: #f1f5f9;
        --text-secondary: #cbd5e1;
        --text-muted: #94a3b8;
    }

    .artwork-placeholder {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    }

    .switch-slider::before {
        background: #f1f5f9;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --border: #000000;
        --shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.3);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .status-dot {
        animation: none;
    }
}
