"""
WebDAV Client for accessing remote media files
Supports browsing directories and streaming media files
"""
import os
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from urllib.parse import urljoin, urlparse, unquote
import tempfile
import threading
import requests
import xml.etree.ElementTree as ET
from webdav3.client import Client
from webdav3.exceptions import WebDavException

logger = logging.getLogger(__name__)

class WebDAVFile:
    """Represents a file on WebDAV server"""
    
    def __init__(self, name: str, path: str, size: int = 0, is_dir: bool = False, modified: str = ""):
        self.name = name
        self.path = path
        self.size = size
        self.is_dir = is_dir
        self.modified = modified
        self.is_media = self._is_media_file()
    
    def _is_media_file(self) -> bool:
        """Check if file is a media file based on extension"""
        if self.is_dir:
            return False

        media_extensions = {
            # Audio
            '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a', '.aiff', '.au', '.ra',
            # Video
            '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.mpg', '.mpeg'
        }

        return Path(self.name).suffix.lower() in media_extensions
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'name': self.name,
            'path': self.path,
            'size': self.size,
            'is_dir': self.is_dir,
            'is_media': self.is_media,
            'modified': self.modified
        }

class WebDAVClient:
    """WebDAV client for remote file access"""
    
    def __init__(self, url: str, username: str = "", password: str = ""):
        self.url = url.rstrip('/')
        self.username = username
        self.password = password
        self.client = None
        self.is_connected = False
        self._cache = {}
        self._cache_lock = threading.Lock()
        
        if url and username:
            self.connect()
    
    def connect(self) -> bool:
        """Connect to WebDAV server"""
        try:
            options = {
                'webdav_hostname': self.url,
                'webdav_login': self.username,
                'webdav_password': self.password,
                'webdav_timeout': 30,
            }
            
            self.client = Client(options)
            
            # Test connection
            self.client.list('/')
            self.is_connected = True
            logger.info(f"Connected to WebDAV server: {self.url}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to WebDAV server: {e}")
            self.is_connected = False
            return False
    
    def disconnect(self):
        """Disconnect from WebDAV server"""
        self.client = None
        self.is_connected = False
        with self._cache_lock:
            self._cache.clear()
        logger.info("Disconnected from WebDAV server")
    
    def _custom_list_directory(self, path: str) -> List[Dict[str, Any]]:
        """Custom WebDAV directory listing using raw PROPFIND request"""
        if not self.is_connected:
            return []

        # Prepare the PROPFIND request
        propfind_body = '''<?xml version="1.0" encoding="utf-8"?>
        <D:propfind xmlns:D="DAV:">
            <D:prop>
                <D:displayname/>
                <D:getcontentlength/>
                <D:getlastmodified/>
                <D:resourcetype/>
                <D:getcontenttype/>
            </D:prop>
        </D:propfind>'''

        headers = {
            'Content-Type': 'application/xml',
            'Depth': '1'
        }

        # Make the request
        url = urljoin(self.url, path)
        auth = (self.username, self.password) if self.username else None

        try:
            response = requests.request('PROPFIND', url,
                                      data=propfind_body,
                                      headers=headers,
                                      auth=auth,
                                      timeout=30)
            response.raise_for_status()

            # Parse the XML response
            root = ET.fromstring(response.content)
            items = []

            # Define namespaces
            namespaces = {'D': 'DAV:'}

            for response_elem in root.findall('.//D:response', namespaces):
                href_elem = response_elem.find('D:href', namespaces)
                if href_elem is None:
                    continue

                item_path = href_elem.text
                if not item_path:
                    continue

                # Skip the directory itself
                if item_path.rstrip('/') == path.rstrip('/'):
                    continue

                # Extract properties
                propstat = response_elem.find('D:propstat', namespaces)
                if propstat is None:
                    continue

                prop = propstat.find('D:prop', namespaces)
                if prop is None:
                    continue

                # Get display name
                displayname_elem = prop.find('D:displayname', namespaces)
                if displayname_elem is not None and displayname_elem.text:
                    name = displayname_elem.text
                else:
                    # Fallback to extracting name from path and decode URL encoding
                    name = os.path.basename(item_path.rstrip('/'))
                    name = unquote(name)  # Decode URL-encoded characters (like Chinese)

                # Check if it's a directory
                resourcetype_elem = prop.find('D:resourcetype', namespaces)
                is_dir = resourcetype_elem is not None and resourcetype_elem.find('D:collection', namespaces) is not None

                # Get size
                size_elem = prop.find('D:getcontentlength', namespaces)
                size = int(size_elem.text) if size_elem is not None and size_elem.text else 0

                # Get modified date
                modified_elem = prop.find('D:getlastmodified', namespaces)
                modified = modified_elem.text if modified_elem is not None and modified_elem.text else ''

                items.append({
                    'name': name,
                    'path': item_path,
                    'isdir': is_dir,
                    'size': size,
                    'modified': modified
                })

            return items

        except Exception as e:
            logger.error(f"Custom PROPFIND failed for {path}: {e}")
            return []

    def list_directory(self, path: str = "/") -> List[WebDAVFile]:
        """List files and directories in the given path"""
        if not self.is_connected:
            logger.warning("Not connected to WebDAV server")
            return []
        
        # Check cache first
        cache_key = f"list:{path}"
        with self._cache_lock:
            if cache_key in self._cache:
                return self._cache[cache_key]
        
        try:
            path = path.strip('/')
            if path:
                path = '/' + path + '/'
            else:
                path = '/'

            logger.debug(f"Listing WebDAV directory: {path}")

            # Try with get_info=True first, fallback to custom method if it fails
            try:
                items = self.client.list(path, get_info=True)
                logger.debug(f"WebDAV returned {len(items)} items with info: {items}")
            except Exception as e:
                logger.warning(f"Failed to list with get_info=True: {e}, trying custom PROPFIND")
                try:
                    # Use custom PROPFIND method that doesn't trigger check
                    items = self._custom_list_directory(path)
                    logger.debug(f"Custom PROPFIND returned {len(items)} items: {items}")
                except Exception as e2:
                    logger.error(f"Failed to list directory with custom method: {e2}")
                    raise e2

            files = []

            for item in items:
                logger.debug(f"Processing WebDAV item: {item} (type: {type(item)})")

                # Handle both string and dict items from webdav3 library
                if isinstance(item, dict):
                    # New format from webdav3 library
                    item_path = item.get('path', '')
                    logger.debug(f"Item path: '{item_path}', Directory path: '{path}', Stripped: '{path.rstrip('/')}'")

                    # Skip the directory itself - be more flexible with path comparison
                    if item_path == path.rstrip('/') or item_path == path:
                        logger.debug(f"Skipping directory itself: {item_path}")
                        continue

                    try:
                        name = os.path.basename(item_path.rstrip('/'))
                        name = unquote(name)  # Decode URL-encoded characters (like Chinese)
                        if not name:  # Skip empty names
                            logger.debug(f"Skipping item with empty name: {item_path}")
                            continue

                        is_dir = item.get('isdir', False)
                        size = int(item.get('size', 0)) if item.get('size') else 0
                        modified = item.get('modified', '')

                        webdav_file = WebDAVFile(
                            name=name,
                            path=item_path,
                            size=size,
                            is_dir=is_dir,
                            modified=modified
                        )
                        files.append(webdav_file)
                        logger.debug(f"Added file: {name} (dir: {is_dir})")

                    except Exception as e:
                        logger.warning(f"Error processing WebDAV item {item}: {e}")
                        # Create basic file entry
                        name = os.path.basename(item_path.rstrip('/')) if item_path else 'Unknown'
                        if name and name != 'Unknown':
                            webdav_file = WebDAVFile(name=name, path=item_path)
                            files.append(webdav_file)

                else:
                    # Old string format
                    logger.debug(f"Processing string item: '{item}', Directory path: '{path}', Stripped: '{path.rstrip('/')}'")

                    # Skip the directory itself - be more flexible with path comparison
                    if item == path.rstrip('/') or item == path:
                        logger.debug(f"Skipping directory itself: {item}")
                        continue

                    try:
                        # Try to get detailed info, but don't fail if it's not supported
                        try:
                            info = self.client.info(item)
                            is_dir = info.get('isdir', False)
                            size = int(info.get('size', 0)) if info.get('size') else 0
                            modified = info.get('modified', '')
                        except Exception as info_error:
                            logger.debug(f"Could not get detailed info for {item}: {info_error}")
                            # Guess if it's a directory based on trailing slash
                            is_dir = item.endswith('/')
                            size = 0
                            modified = ''

                        name = os.path.basename(item.rstrip('/'))
                        name = unquote(name)  # Decode URL-encoded characters (like Chinese)
                        if not name:  # Skip empty names
                            logger.debug(f"Skipping item with empty name: {item}")
                            continue

                        webdav_file = WebDAVFile(
                            name=name,
                            path=item,
                            size=size,
                            is_dir=is_dir,
                            modified=modified
                        )
                        files.append(webdav_file)
                        logger.debug(f"Added file: {name} (dir: {is_dir})")

                    except Exception as e:
                        logger.warning(f"Error processing string item {item}: {e}")
                        # Create basic file entry as fallback
                        name = os.path.basename(item.rstrip('/'))
                        name = unquote(name)  # Decode URL-encoded characters (like Chinese)
                        if name:
                            is_dir = item.endswith('/')
                            webdav_file = WebDAVFile(name=name, path=item, is_dir=is_dir)
                            files.append(webdav_file)
            
            # Cache the result
            with self._cache_lock:
                self._cache[cache_key] = files
            
            logger.debug(f"Listed {len(files)} items in {path}")
            return files
            
        except Exception as e:
            logger.error(f"Error listing directory {path}: {e}")
            return []
    
    def get_media_files(self, path: str = "/", recursive: bool = False) -> List[WebDAVFile]:
        """Get all media files in the given path"""
        media_files = []
        
        try:
            files = self.list_directory(path)
            
            for file in files:
                if file.is_media:
                    media_files.append(file)
                elif file.is_dir and recursive:
                    # Recursively get media files from subdirectories
                    sub_media = self.get_media_files(file.path, recursive=True)
                    media_files.extend(sub_media)
            
            logger.debug(f"Found {len(media_files)} media files in {path}")
            return media_files
            
        except Exception as e:
            logger.error(f"Error getting media files from {path}: {e}")
            return []
    
    def file_exists(self, path: str) -> bool:
        """Check if a file exists on the server"""
        if not self.is_connected:
            return False
        
        try:
            return self.client.check(path)
        except Exception as e:
            logger.error(f"Error checking file existence {path}: {e}")
            return False
    
    def get_file_info(self, path: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a file"""
        if not self.is_connected:
            return None
        
        try:
            return self.client.info(path)
        except Exception as e:
            logger.error(f"Error getting file info {path}: {e}")
            return None
    
    def get_streaming_url(self, path: str) -> str:
        """Get streaming URL for a media file"""
        if not self.is_connected:
            return ""

        # Normalize the path - ensure it starts with /
        if not path.startswith('/'):
            path = '/' + path

        # For WebDAV, we can usually stream directly from the URL
        # Construct the full URL
        parsed_url = urlparse(self.url)
        if self.username and self.password:
            # Include credentials in URL for VLC
            streaming_url = f"{parsed_url.scheme}://{self.username}:{self.password}@{parsed_url.netloc}{path}"
        else:
            streaming_url = urljoin(self.url, path)

        logger.debug(f"Constructed streaming URL: {streaming_url}")
        return streaming_url
    
    def download_file(self, remote_path: str, local_path: str) -> bool:
        """Download a file from WebDAV server to local storage"""
        if not self.is_connected:
            return False
        
        try:
            self.client.download_sync(remote_path, local_path)
            logger.info(f"Downloaded {remote_path} to {local_path}")
            return True
        except Exception as e:
            logger.error(f"Error downloading file {remote_path}: {e}")
            return False
    
    def create_temp_file(self, remote_path: str) -> Optional[str]:
        """Download file to temporary location and return local path"""
        if not self.is_connected:
            return None
        
        try:
            # Create temporary file
            suffix = Path(remote_path).suffix
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=suffix)
            temp_path = temp_file.name
            temp_file.close()
            
            # Download to temporary file
            if self.download_file(remote_path, temp_path):
                return temp_path
            else:
                os.unlink(temp_path)
                return None
                
        except Exception as e:
            logger.error(f"Error creating temp file for {remote_path}: {e}")
            return None
    
    def search_files(self, query: str, path: str = "/", recursive: bool = True) -> List[WebDAVFile]:
        """Search for files matching the query"""
        matching_files = []
        query_lower = query.lower()
        
        try:
            files = self.list_directory(path)
            
            for file in files:
                if query_lower in file.name.lower():
                    matching_files.append(file)
                
                if file.is_dir and recursive:
                    sub_matches = self.search_files(query, file.path, recursive=True)
                    matching_files.extend(sub_matches)
            
            logger.debug(f"Found {len(matching_files)} files matching '{query}'")
            return matching_files
            
        except Exception as e:
            logger.error(f"Error searching files: {e}")
            return []
    
    def clear_cache(self):
        """Clear the directory listing cache"""
        with self._cache_lock:
            self._cache.clear()
        logger.debug("WebDAV cache cleared")
    
    def get_connection_status(self) -> Dict[str, Any]:
        """Get connection status information"""
        return {
            'connected': self.is_connected,
            'url': self.url,
            'username': self.username,
            'cache_size': len(self._cache)
        }
    
    def test_connection(self) -> Tuple[bool, str]:
        """Test the WebDAV connection"""
        try:
            if not self.client:
                return False, "No client configured"
            
            # Try to list root directory
            self.client.list('/')
            return True, "Connection successful"
            
        except WebDavException as e:
            return False, f"WebDAV error: {str(e)}"
        except Exception as e:
            return False, f"Connection error: {str(e)}"
