"""
Scheduler and Automation Engine
Handles automatic playback scheduling, time-based triggers, and playlist management
"""
import schedule
import threading
import time
import logging
from datetime import datetime, time as dt_time
from typing import Dict, List, Optional, Callable
from enum import Enum
import json
from pathlib import Path

from config.settings import Config<PERSON>anager, ScheduleEntry, Playlist
from core.vlc_controller import <PERSON><PERSON><PERSON><PERSON>roll<PERSON>, PlaybackState
from core.webdav_client import WebDAVClient

logger = logging.getLogger(__name__)

class SchedulerState(Enum):
    """Scheduler states"""
    STOPPED = "stopped"
    RUNNING = "running"
    PAUSED = "paused"

class PlaybackSession:
    """Represents an active playback session"""

    def __init__(self, schedule_entry: ScheduleEntry, playlist: Playlist):
        self.schedule_entry = schedule_entry
        self.playlist = playlist
        self.start_time = datetime.now()
        self.end_time: Optional[datetime] = None
        self.files_played = []
        self.current_file = None
        self.is_active = True

class AutomationScheduler:
    """Main scheduler for automated playback"""
    
    def __init__(self, config_manager: Config<PERSON><PERSON><PERSON>, vlc_controller: V<PERSON><PERSON>ontroller, webdav_client: WebDAVClient, webdav2_client: Optional[WebDAVClient] = None):
        self.config_manager = config_manager
        self.vlc_controller = vlc_controller
        self.webdav_client = webdav_client
        self.webdav2_client = webdav2_client
        
        self.state = SchedulerState.STOPPED
        self.scheduler_thread = None
        self.current_session: Optional[PlaybackSession] = None
        self.stop_event = threading.Event()
        self.state_callbacks: List[Callable] = []
        
        # Schedule management
        self.active_schedules: Dict[str, schedule.Job] = {}
        self.schedule_history = []
        
        # Setup VLC callbacks
        self.vlc_controller.add_state_callback(self._on_vlc_state_change)
    
    def start(self):
        """Start the scheduler"""
        if self.state == SchedulerState.RUNNING:
            logger.warning("Scheduler already running")
            return
        
        self.state = SchedulerState.RUNNING
        self.stop_event.clear()
        
        # Load and setup schedules
        self._setup_schedules()
        
        # Start scheduler thread
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        
        logger.info("Automation scheduler started")
        self._notify_state_change()
    
    def stop(self):
        """Stop the scheduler"""
        if self.state == SchedulerState.STOPPED:
            return
        
        self.state = SchedulerState.STOPPED
        self.stop_event.set()
        
        # Stop current playback
        if self.current_session and self.current_session.is_active:
            self._stop_current_session()
        
        # Clear all scheduled jobs
        schedule.clear()
        self.active_schedules.clear()
        
        # Wait for scheduler thread to finish
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        logger.info("Automation scheduler stopped")
        self._notify_state_change()
    
    def pause(self):
        """Pause the scheduler"""
        if self.state != SchedulerState.RUNNING:
            return
        
        self.state = SchedulerState.PAUSED
        
        # Pause current playback
        if self.current_session and self.current_session.is_active:
            self.vlc_controller.pause()
        
        logger.info("Automation scheduler paused")
        self._notify_state_change()
    
    def resume(self):
        """Resume the scheduler"""
        if self.state != SchedulerState.PAUSED:
            return
        
        self.state = SchedulerState.RUNNING
        
        # Resume current playback
        if self.current_session and self.current_session.is_active:
            self.vlc_controller.play()
        
        logger.info("Automation scheduler resumed")
        self._notify_state_change()
    
    def _setup_schedules(self):
        """Setup all scheduled jobs"""
        schedule.clear()
        self.active_schedules.clear()
        
        for schedule_entry in self.config_manager.schedules:
            if not schedule_entry.enabled:
                continue
            
            self._add_schedule_job(schedule_entry)
        
        logger.info(f"Setup {len(self.active_schedules)} scheduled jobs")
    
    def _add_schedule_job(self, schedule_entry: ScheduleEntry):
        """Add a single schedule job"""
        try:
            # Apply day-of-week filters if specified
            if schedule_entry.days_of_week:
                # Create jobs for specific days
                self._create_day_specific_jobs(schedule_entry)
            else:
                # Create daily jobs
                start_job = schedule.every().day.at(schedule_entry.start_time).do(
                    self._start_scheduled_playback, schedule_entry
                )

                stop_job = schedule.every().day.at(schedule_entry.end_time).do(
                    self._stop_scheduled_playback, schedule_entry
                )

                self.active_schedules[f"{schedule_entry.name}_start"] = start_job
                self.active_schedules[f"{schedule_entry.name}_stop"] = stop_job

            logger.debug(f"Added schedule job: {schedule_entry.name}")

        except Exception as e:
            logger.error(f"Error adding schedule job {schedule_entry.name}: {e}")

    def _create_day_specific_jobs(self, schedule_entry: ScheduleEntry):
        """Create jobs for specific days of the week"""
        day_names = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']

        for day_num in schedule_entry.days_of_week:
            if 0 <= day_num <= 6:
                day_name = day_names[day_num]

                # Create start job for this day
                start_job = getattr(schedule.every(), day_name).at(schedule_entry.start_time).do(
                    self._start_scheduled_playback, schedule_entry
                )

                # Create stop job for this day
                stop_job = getattr(schedule.every(), day_name).at(schedule_entry.end_time).do(
                    self._stop_scheduled_playback, schedule_entry
                )

                # Store jobs with day-specific keys
                self.active_schedules[f"{schedule_entry.name}_{day_name}_start"] = start_job
                self.active_schedules[f"{schedule_entry.name}_{day_name}_stop"] = stop_job


    
    def _scheduler_loop(self):
        """Main scheduler loop"""
        while not self.stop_event.is_set():
            try:
                if self.state == SchedulerState.RUNNING:
                    schedule.run_pending()
                
                time.sleep(1)  # Check every second
                
            except Exception as e:
                logger.error(f"Error in scheduler loop: {e}")
                time.sleep(5)  # Wait before retrying
    
    def _start_scheduled_playback(self, schedule_entry: ScheduleEntry):
        """Start playback for a scheduled entry"""
        if self.state != SchedulerState.RUNNING:
            logger.info(f"Skipping scheduled playback {schedule_entry.name} - scheduler not running")
            return
        
        logger.info(f"Starting scheduled playback: {schedule_entry.name}")
        
        try:
            # Stop current session if active
            if self.current_session and self.current_session.is_active:
                self._stop_current_session()
            
            # Get playlist
            playlist = self.config_manager.get_playlist(schedule_entry.playlist_name)
            if not playlist:
                logger.error(f"Playlist not found: {schedule_entry.playlist_name}")
                return
            
            # Create new session
            self.current_session = PlaybackSession(schedule_entry, playlist)
            
            # Prepare file paths
            file_paths = self._prepare_file_paths(playlist)
            if not file_paths:
                logger.error(f"No valid files found in playlist: {playlist.name}")
                return
            
            # Load playlist in VLC
            if not self.vlc_controller.load_playlist(file_paths, playlist.shuffle, playlist.name):
                logger.error(f"Failed to load playlist in VLC: {playlist.name}")
                return
            
            # Set volume if specified
            volume = schedule_entry.volume or playlist.volume
            self.vlc_controller.set_volume(volume)
            
            # Start playback
            if self.vlc_controller.play():
                logger.info(f"Started playback for schedule: {schedule_entry.name}")
                self._add_to_history(schedule_entry, "started")
            else:
                logger.error(f"Failed to start playback for schedule: {schedule_entry.name}")
                self.current_session = None
            
        except Exception as e:
            logger.error(f"Error starting scheduled playback {schedule_entry.name}: {e}")
            self.current_session = None
    
    def _stop_scheduled_playback(self, schedule_entry: ScheduleEntry):
        """Stop playback for a scheduled entry"""
        logger.info(f"Stopping scheduled playback: {schedule_entry.name}")
        
        try:
            if (self.current_session and 
                self.current_session.is_active and 
                self.current_session.schedule_entry.name == schedule_entry.name):
                
                self._stop_current_session()
                self._add_to_history(schedule_entry, "stopped")
            
        except Exception as e:
            logger.error(f"Error stopping scheduled playback {schedule_entry.name}: {e}")
    
    def _stop_current_session(self):
        """Stop the current playback session"""
        if not self.current_session:
            return
        
        try:
            self.vlc_controller.stop()
            self.current_session.is_active = False
            self.current_session.end_time = datetime.now()
            
            logger.info(f"Stopped playback session: {self.current_session.schedule_entry.name}")
            
        except Exception as e:
            logger.error(f"Error stopping current session: {e}")
    
    def _prepare_file_paths(self, playlist: Playlist) -> List[str]:
        """Prepare file paths for VLC, handling both local and WebDAV files"""
        file_paths = []
        
        for item in playlist.items:
            if not item.enabled:
                continue
            
            try:
                if item.is_webdav:
                    # Handle WebDAV files - determine which server to use
                    webdav_server = getattr(item, 'webdav_server', 1)  # Default to server 1 for backward compatibility

                    if webdav_server == 2 and self.webdav2_client:
                        # Use WebDAV2 server
                        if self.webdav2_client.is_connected:
                            streaming_url = self.webdav2_client.get_streaming_url(item.path)
                            if streaming_url:
                                file_paths.append(streaming_url)
                            else:
                                logger.warning(f"Could not get streaming URL for WebDAV2 file: {item.path}")
                        else:
                            logger.warning(f"WebDAV2 not connected, skipping: {item.path}")
                    else:
                        # Use WebDAV server 1 (default)
                        if self.webdav_client.is_connected:
                            streaming_url = self.webdav_client.get_streaming_url(item.path)
                            if streaming_url:
                                file_paths.append(streaming_url)
                            else:
                                logger.warning(f"Could not get streaming URL for WebDAV file: {item.path}")
                        else:
                            logger.warning(f"WebDAV not connected, skipping: {item.path}")
                else:
                    # Handle local files
                    if Path(item.path).exists():
                        file_paths.append(item.path)
                    else:
                        logger.warning(f"Local file not found: {item.path}")
                
            except Exception as e:
                logger.error(f"Error preparing file path {item.path}: {e}")
        
        return file_paths
    
    def _on_vlc_state_change(self, state: PlaybackState):
        """Handle VLC state changes"""
        if not self.current_session:
            return
        
        try:
            if state == PlaybackState.ENDED:
                # Track completed playback
                media_info = self.vlc_controller.get_current_media_info()
                if media_info.get('mrl'):
                    self.current_session.files_played.append(media_info['mrl'])
            
            elif state == PlaybackState.ERROR:
                logger.error("VLC playback error during scheduled session")
                # Could implement retry logic here
            
        except Exception as e:
            logger.error(f"Error handling VLC state change: {e}")
    
    def _add_to_history(self, schedule_entry: ScheduleEntry, action: str):
        """Add entry to playback history"""
        history_entry = {
            'timestamp': datetime.now().isoformat(),
            'schedule_name': schedule_entry.name,
            'playlist_name': schedule_entry.playlist_name,
            'action': action
        }
        
        self.schedule_history.append(history_entry)
        
        # Keep only last 100 entries
        if len(self.schedule_history) > 100:
            self.schedule_history = self.schedule_history[-100:]
    
    def add_state_callback(self, callback: Callable):
        """Add a callback for state changes"""
        self.state_callbacks.append(callback)
    
    def remove_state_callback(self, callback: Callable):
        """Remove a state change callback"""
        if callback in self.state_callbacks:
            self.state_callbacks.remove(callback)
    
    def _notify_state_change(self):
        """Notify registered callbacks of state changes"""
        for callback in self.state_callbacks:
            try:
                callback(self.state)
            except Exception as e:
                logger.error(f"Error in scheduler state callback: {e}")
    
    def get_status(self) -> Dict:
        """Get current scheduler status"""
        status = {
            'state': self.state.value,
            'active_schedules': len(self.active_schedules),
            'current_session': None,
            'next_scheduled': self._get_next_scheduled_time(),
            'history_count': len(self.schedule_history)
        }
        
        if self.current_session and self.current_session.is_active:
            status['current_session'] = {
                'schedule_name': self.current_session.schedule_entry.name,
                'playlist_name': self.current_session.playlist.name,
                'start_time': self.current_session.start_time.isoformat(),
                'files_played': len(self.current_session.files_played)
            }
        
        return status
    
    def _get_next_scheduled_time(self) -> Optional[str]:
        """Get the next scheduled playback time"""
        try:
            next_run = schedule.next_run()
            if next_run:
                return next_run.isoformat()
        except Exception as e:
            logger.error(f"Error getting next scheduled time: {e}")
        
        return None
    
    def reload_schedules(self):
        """Reload schedules from configuration"""
        # First reload the configuration from file
        self.config_manager.load_schedules()

        if self.state == SchedulerState.RUNNING:
            self._setup_schedules()
            logger.info("Schedules reloaded")
    
    def force_start_schedule(self, schedule_name: str) -> bool:
        """Manually start a specific schedule"""
        schedule_entry = self.config_manager.get_schedule(schedule_name)
        if not schedule_entry:
            logger.error(f"Schedule not found: {schedule_name}")
            return False
        
        self._start_scheduled_playback(schedule_entry)
        return True
    
    def force_stop_current(self) -> bool:
        """Manually stop current playback"""
        if self.current_session and self.current_session.is_active:
            self._stop_current_session()
            return True
        return False
    
    def get_history(self, limit: int = 50) -> List[Dict]:
        """Get playback history"""
        return self.schedule_history[-limit:] if self.schedule_history else []
