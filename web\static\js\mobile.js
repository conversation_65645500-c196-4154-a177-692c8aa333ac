/**
 * VLC Automation System - Mobile Frontend JavaScript
 * Optimized for mobile devices with touch interactions
 */

class MobileVLCApp {
    constructor() {
        this.currentTab = 'playlists';
        this.currentBrowser = 'local';
        this.playlists = {};
        this.schedules = [];
        this.status = {};

        // Internationalization
        this.currentLanguage = 'en';
        this.translations = {};

        // File browser state
        this.browserHistory = {
            local: [],
            webdav: [],
            webdav2: []
        };
        this.browserHistoryIndex = {
            local: -1,
            webdav: -1,
            webdav2: -1
        };
        this.currentPath = {
            local: 'data',
            webdav: '/',
            webdav2: '/dav'
        };
        this.currentFiles = {
            local: [],
            webdav: [],
            webdav2: []
        };
        this.filteredFiles = {
            local: [],
            webdav: [],
            webdav2: []
        };
        this.searchQuery = '';
        this.fileTypeFilter = 'all';

        // File selection state
        this.selectedFiles = {
            local: new Set(),
            webdav: new Set(),
            webdav2: new Set()
        };

        // Touch and gesture handling
        this.touchStartY = 0;
        this.touchStartX = 0;
        this.isScrolling = false;
        this.lastTap = 0;

        // Modal state
        this.activeModal = null;

        // Media state
        this.isPlaying = false;
        this.currentVolume = 70;
        this.currentPosition = 0;
        this.totalDuration = 0;

        this.init();
    }

    async init() {
        await this.initializeI18n();
        this.setupEventListeners();
        this.setupTouchHandlers();
        this.setupModals();
        this.loadInitialData();
        this.startStatusUpdates();
        this.setupSwipeGestures();
    }

    setupEventListeners() {
        // Header actions
        document.getElementById('languageBtn').addEventListener('click', () => this.showLanguageModal());
        document.getElementById('settingsBtn').addEventListener('click', () => this.showSettingsModal());
        document.getElementById('logoutBtn').addEventListener('click', () => this.logout());

        // Media controls (compact) - with null checks
        const playPauseBtnCompact = document.getElementById('playPauseBtnCompact');
        if (playPauseBtnCompact) {
            playPauseBtnCompact.addEventListener('click', () => this.togglePlayPause());
        }

        const prevBtnCompact = document.getElementById('prevBtnCompact');
        if (prevBtnCompact) {
            prevBtnCompact.addEventListener('click', () => this.vlcControl('previous'));
        }

        const nextBtnCompact = document.getElementById('nextBtnCompact');
        if (nextBtnCompact) {
            nextBtnCompact.addEventListener('click', () => this.vlcControl('next'));
        }

        const stopBtnCompact = document.getElementById('stopBtnCompact');
        if (stopBtnCompact) {
            stopBtnCompact.addEventListener('click', () => this.vlcControl('stop'));
        }

        const repeatBtnCompact = document.getElementById('repeatBtnCompact');
        if (repeatBtnCompact) {
            repeatBtnCompact.addEventListener('click', () => this.toggleRepeat());
        }

        // Volume control (compact) - with null checks
        const volumeBtnCompact = document.getElementById('volumeBtnCompact');
        if (volumeBtnCompact) {
            volumeBtnCompact.addEventListener('click', () => this.toggleVolumeSliderCompact());
        }

        const volumeSliderCompact = document.getElementById('volumeSliderCompact');
        if (volumeSliderCompact) {
            volumeSliderCompact.addEventListener('input', (e) => this.handleVolumeChange(e));
        }

        // Progress bar (compact)
        this.setupProgressBarCompact();

        // Scheduler controls
        document.getElementById('startSchedulerBtn').addEventListener('click', () => this.schedulerControl('start'));
        document.getElementById('pauseSchedulerBtn').addEventListener('click', () => this.schedulerControl('pause'));
        document.getElementById('stopSchedulerBtn').addEventListener('click', () => this.schedulerControl('stop'));

        // Bottom navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.switchTab(e.target.closest('.nav-btn').dataset.tab);
            });
        });

        // Tab content buttons
        document.getElementById('addPlaylistBtn').addEventListener('click', () => this.showPlaylistModal());
        document.getElementById('addScheduleBtn').addEventListener('click', () => this.showScheduleModal());

        // File browser
        this.setupFileBrowser();

        // Form submissions
        this.setupForms();

        // Prevent accidental navigation
        this.preventAccidentalNavigation();
    }

    setupTouchHandlers() {
        // Prevent zoom on double tap for control buttons
        document.querySelectorAll('.control-btn, .nav-btn, .btn').forEach(btn => {
            btn.addEventListener('touchend', (e) => {
                const now = new Date().getTime();
                const timeSince = now - this.lastTap;
                if (timeSince < 600 && timeSince > 0) {
                    e.preventDefault();
                }
                this.lastTap = now;
            });
        });

        // Handle touch feedback
        document.querySelectorAll('.control-btn, .nav-btn, .btn, .file-item, .playlist-card, .schedule-card').forEach(element => {
            element.addEventListener('touchstart', (e) => {
                element.style.transform = 'scale(0.95)';
            });

            element.addEventListener('touchend', (e) => {
                setTimeout(() => {
                    element.style.transform = '';
                }, 100);
            });

            element.addEventListener('touchcancel', (e) => {
                element.style.transform = '';
            });
        });
    }

    setupSwipeGestures() {
        const tabContainer = document.querySelector('.tab-container');
        let startX = 0;
        let startY = 0;
        let isHorizontalSwipe = false;

        tabContainer.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            isHorizontalSwipe = false;
        });

        tabContainer.addEventListener('touchmove', (e) => {
            if (!startX || !startY) return;

            const currentX = e.touches[0].clientX;
            const currentY = e.touches[0].clientY;
            const diffX = Math.abs(currentX - startX);
            const diffY = Math.abs(currentY - startY);

            if (diffX > diffY && diffX > 50) {
                isHorizontalSwipe = true;
            }
        });

        tabContainer.addEventListener('touchend', (e) => {
            if (!isHorizontalSwipe || !startX) return;

            const endX = e.changedTouches[0].clientX;
            const diffX = startX - endX;

            if (Math.abs(diffX) > 100) {
                if (diffX > 0) {
                    // Swipe left - next tab
                    this.switchToNextTab();
                } else {
                    // Swipe right - previous tab
                    this.switchToPreviousTab();
                }
            }

            startX = 0;
            startY = 0;
            isHorizontalSwipe = false;
        });
    }

    setupProgressBar() {
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');
        const progressHandle = document.getElementById('progressHandle');
        let isDragging = false;

        const updateProgress = (clientX) => {
            const rect = progressBar.getBoundingClientRect();
            const percentage = Math.max(0, Math.min(100, ((clientX - rect.left) / rect.width) * 100));
            progressFill.style.width = percentage + '%';
            progressHandle.style.left = percentage + '%';
            
            if (this.totalDuration > 0) {
                const newPosition = (percentage / 100) * this.totalDuration;
                this.seekTo(newPosition);
            }
        };

        // Mouse events
        progressBar.addEventListener('mousedown', (e) => {
            isDragging = true;
            updateProgress(e.clientX);
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                updateProgress(e.clientX);
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
        });

        // Touch events
        progressBar.addEventListener('touchstart', (e) => {
            isDragging = true;
            updateProgress(e.touches[0].clientX);
            e.preventDefault();
        });

        document.addEventListener('touchmove', (e) => {
            if (isDragging) {
                updateProgress(e.touches[0].clientX);
                e.preventDefault();
            }
        });

        document.addEventListener('touchend', () => {
            isDragging = false;
        });
    }

    setupProgressBarCompact() {
        const progressBar = document.getElementById('progressBarCompact');
        const progressFill = document.getElementById('progressFillCompact');

        if (!progressBar || !progressFill) {
            console.warn('Progress bar compact elements not found');
            return;
        }

        let isDragging = false;

        const updateProgress = (clientX) => {
            const rect = progressBar.getBoundingClientRect();
            const percentage = Math.max(0, Math.min(100, ((clientX - rect.left) / rect.width) * 100));
            progressFill.style.width = percentage + '%';

            if (this.totalDuration > 0) {
                const newPosition = (percentage / 100) * this.totalDuration;
                this.seekTo(newPosition);
            }
        };

        // Mouse events
        progressBar.addEventListener('mousedown', (e) => {
            isDragging = true;
            updateProgress(e.clientX);
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                updateProgress(e.clientX);
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
        });

        // Touch events
        progressBar.addEventListener('touchstart', (e) => {
            isDragging = true;
            updateProgress(e.touches[0].clientX);
            e.preventDefault();
        });

        document.addEventListener('touchmove', (e) => {
            if (isDragging) {
                updateProgress(e.touches[0].clientX);
                e.preventDefault();
            }
        });

        document.addEventListener('touchend', () => {
            isDragging = false;
        });
    }

    setupModals() {
        // Close modals when clicking overlay
        document.querySelectorAll('.modal-overlay').forEach(overlay => {
            overlay.addEventListener('click', () => this.closeActiveModal());
        });

        // Close buttons
        document.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => this.closeActiveModal());
        });

        // Language modal
        document.querySelectorAll('.language-option').forEach(option => {
            option.addEventListener('click', (e) => {
                const lang = e.currentTarget.dataset.lang;
                this.changeLanguage(lang);
                this.closeActiveModal();
            });
        });

        // Settings tabs
        document.querySelectorAll('.settings-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabName = e.target.dataset.settingsTab;
                this.switchSettingsTab(tabName);
            });
        });
    }

    setupFileBrowser() {
        // Browser tabs
        document.querySelectorAll('.browser-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const browser = e.target.closest('.browser-tab').dataset.browser;
                this.switchBrowser(browser);
            });
        });

        // Navigation controls
        document.getElementById('backBtn').addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.navigateBack();
        });
        document.getElementById('upBtn').addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.navigateUp();
        });
        document.getElementById('homeBtn').addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.navigateHome();
        });
        document.getElementById('refreshBtn').addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.refreshCurrentPath();
        });

        // Search and filter
        document.getElementById('fileSearchInput').addEventListener('input', (e) => this.handleSearch(e.target.value));
        document.getElementById('searchBtn').addEventListener('click', () => this.performSearch());
        document.getElementById('fileTypeFilter').addEventListener('change', (e) => this.handleFilterChange(e.target.value));

        // Selection controls
        document.getElementById('selectAllBtn').addEventListener('click', () => this.selectAllFiles());
        document.getElementById('clearSelectionBtn').addEventListener('click', () => this.clearSelection());

        // Playlist actions
        document.getElementById('createPlaylistFromSelection').addEventListener('click', () => this.showQuickPlaylistModal());
        document.getElementById('addToPlaylistBtn').addEventListener('click', () => this.addSelectedToPlaylist());
    }

    setupForms() {
        // Playlist form
        document.getElementById('savePlaylistBtn').addEventListener('click', () => this.savePlaylist());
        document.getElementById('cancelPlaylistBtn').addEventListener('click', () => this.closeActiveModal());

        // Quick playlist form
        document.getElementById('createQuickPlaylistBtn').addEventListener('click', () => this.createQuickPlaylist());
        document.getElementById('cancelQuickPlaylistBtn').addEventListener('click', () => this.closeActiveModal());

        // Schedule form
        document.getElementById('saveScheduleBtn').addEventListener('click', () => this.saveSchedule());
        document.getElementById('cancelScheduleBtn').addEventListener('click', () => this.closeActiveModal());

        // Settings forms
        document.getElementById('webdavForm').addEventListener('submit', (e) => this.saveWebDAVSettings(e));
        document.getElementById('webdav2Form').addEventListener('submit', (e) => this.saveWebDAV2Settings(e));

        // Reconnect buttons
        document.getElementById('reconnectWebdavBtn').addEventListener('click', () => this.reconnectWebDAV());
        document.getElementById('reconnectWebdav2Btn').addEventListener('click', () => this.reconnectWebDAV2());

        // Range sliders with live updates
        this.setupRangeSliders();
    }

    preventAccidentalNavigation() {
        // Prevent browser back button from interfering with file browser navigation
        window.addEventListener('popstate', (e) => {
            // If we're in the files tab, prevent browser navigation
            const filesTab = document.getElementById('filesTab');
            if (filesTab && filesTab.classList.contains('active')) {
                e.preventDefault();
                // Push the current state back to prevent actual navigation
                history.pushState(null, null, window.location.href);
            }
        });

        // Push initial state to enable popstate handling
        history.pushState(null, null, window.location.href);

        // Prevent form submissions from causing page navigation
        document.addEventListener('submit', (e) => {
            // Only prevent if it's one of our forms
            if (e.target.matches('#webdavForm, #webdav2Form, #playlistForm, #quickPlaylistForm, #scheduleForm')) {
                e.preventDefault();
            }
        });
    }

    setupRangeSliders() {
        // Playlist volume
        document.getElementById('playlistVolume').addEventListener('input', (e) => {
            document.getElementById('playlistVolumeValue').textContent = e.target.value + '%';
        });

        // Quick playlist volume
        document.getElementById('quickPlaylistVolume').addEventListener('input', (e) => {
            document.getElementById('quickPlaylistVolumeValue').textContent = e.target.value + '%';
        });

        // Schedule volume
        document.getElementById('scheduleVolume').addEventListener('input', (e) => {
            document.getElementById('scheduleVolumeValue').textContent = e.target.value + '%';
        });
    }

    // Modal Management
    showLanguageModal() {
        this.showModal('languageModal');
    }

    showSettingsModal() {
        this.showModal('settingsModal');
        this.loadSettings();
    }

    showPlaylistModal(playlist = null) {
        this.showModal('playlistModal');
        if (playlist) {
            this.populatePlaylistForm(playlist);
            document.getElementById('playlistModalTitle').textContent = 'Edit Playlist';
        } else {
            this.clearPlaylistForm();
            document.getElementById('playlistModalTitle').textContent = 'Add Playlist';
        }
    }

    showScheduleModal(schedule = null) {
        this.showModal('scheduleModal');
        this.loadPlaylistsForSchedule();
        if (schedule) {
            this.populateScheduleForm(schedule);
            document.getElementById('scheduleModalTitle').textContent = 'Edit Schedule';
        } else {
            this.clearScheduleForm();
            document.getElementById('scheduleModalTitle').textContent = 'Add Schedule';
        }
    }

    showQuickPlaylistModal() {
        const selectedCount = this.selectedFiles[this.currentBrowser].size;
        if (selectedCount === 0) {
            this.showNotification('Please select files first', 'warning');
            return;
        }
        this.showModal('quickPlaylistModal');
        this.updateSelectedFilesPreview();
    }

    showModal(modalId) {
        this.closeActiveModal();
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('show');
            this.activeModal = modalId;
            document.body.style.overflow = 'hidden';
        }
    }

    closeActiveModal() {
        if (this.activeModal) {
            const modal = document.getElementById(this.activeModal);
            if (modal) {
                modal.classList.remove('show');
            }
            this.activeModal = null;
            document.body.style.overflow = '';
        }
    }

    // Tab Management
    switchTab(tabName) {
        // Update navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });

        // Update content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.toggle('active', content.id === tabName + 'Tab');
        });

        this.currentTab = tabName;

        // Load tab-specific data
        switch (tabName) {
            case 'playlists':
                this.loadPlaylists();
                break;
            case 'schedules':
                this.loadSchedules();
                break;
            case 'files':
                this.loadFiles();
                break;
        }
    }

    switchToNextTab() {
        const tabs = ['playlists', 'schedules', 'files'];
        const currentIndex = tabs.indexOf(this.currentTab);
        const nextIndex = (currentIndex + 1) % tabs.length;
        this.switchTab(tabs[nextIndex]);
    }

    switchToPreviousTab() {
        const tabs = ['playlists', 'schedules', 'files'];
        const currentIndex = tabs.indexOf(this.currentTab);
        const prevIndex = (currentIndex - 1 + tabs.length) % tabs.length;
        this.switchTab(tabs[prevIndex]);
    }

    switchSettingsTab(tabName) {
        document.querySelectorAll('.settings-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.settingsTab === tabName);
        });

        document.querySelectorAll('.settings-pane').forEach(pane => {
            pane.classList.toggle('active', pane.id === tabName + 'Settings');
        });
    }

    // Media Controls
    togglePlayPause() {
        if (this.isPlaying) {
            this.vlcControl('pause');
        } else {
            this.vlcControl('play');
        }
    }

    handleVolumeChange(e) {
        const volume = e.target.value;
        this.currentVolume = volume;
        const volumeValueCompact = document.getElementById('volumeValueCompact');
        if (volumeValueCompact) {
            volumeValueCompact.textContent = volume + '%';
        }
        this.setVolume(volume);
        this.updateVolumeIcon(volume);
    }

    toggleVolumeSliderCompact() {
        const overlay = document.getElementById('volumeSliderOverlay');
        if (!overlay) {
            console.warn('Volume slider overlay not found');
            return;
        }

        const isVisible = overlay.style.display !== 'none';
        overlay.style.display = isVisible ? 'none' : 'block';

        // Close when clicking outside
        if (!isVisible) {
            const closeHandler = (e) => {
                const volumeBtnCompact = document.getElementById('volumeBtnCompact');
                if (!overlay.contains(e.target) && (!volumeBtnCompact || !volumeBtnCompact.contains(e.target))) {
                    overlay.style.display = 'none';
                    document.removeEventListener('click', closeHandler);
                }
            };
            setTimeout(() => document.addEventListener('click', closeHandler), 100);
        }
    }

    updateVolumeIcon(volume) {
        const icon = document.getElementById('volumeIconCompact');
        if (icon) {
            if (volume == 0) {
                icon.className = 'fas fa-volume-mute';
            } else if (volume < 50) {
                icon.className = 'fas fa-volume-down';
            } else {
                icon.className = 'fas fa-volume-up';
            }
        }
    }

    updatePlayPauseButton(isPlaying) {
        const icon = document.getElementById('playPauseIconCompact');
        if (icon) {
            icon.className = isPlaying ? 'fas fa-pause' : 'fas fa-play';
        }
        this.isPlaying = isPlaying;
    }

    updateProgress(current, total) {
        this.currentPosition = current;
        this.totalDuration = total;

        const percentage = total > 0 ? (current / total) * 100 : 0;

        // Update main progress bar (if exists)
        const progressFill = document.getElementById('progressFill');
        const progressHandle = document.getElementById('progressHandle');
        if (progressFill && progressHandle) {
            progressFill.style.width = percentage + '%';
            progressHandle.style.left = percentage + '%';
        }

        // Update compact progress bar
        const progressFillCompact = document.getElementById('progressFillCompact');
        if (progressFillCompact) {
            progressFillCompact.style.width = percentage + '%';
        }

        // Update time displays
        const currentTime = document.getElementById('currentTime');
        const totalTime = document.getElementById('totalTime');
        if (currentTime && totalTime) {
            currentTime.textContent = this.formatTime(current);
            totalTime.textContent = this.formatTime(total);
        }

        // Update compact time displays
        const currentTimeCompact = document.getElementById('currentTimeCompact');
        const totalTimeCompact = document.getElementById('totalTimeCompact');
        if (currentTimeCompact && totalTimeCompact) {
            currentTimeCompact.textContent = this.formatTime(current);
            totalTimeCompact.textContent = this.formatTime(total);
        }
    }

    formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }

    // File Browser
    switchBrowser(browser) {
        document.querySelectorAll('.browser-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.browser === browser);
        });

        this.currentBrowser = browser;
        this.loadFiles();
        this.updateSelectionUI();
    }

    loadFiles() {
        const path = this.currentPath[this.currentBrowser];
        this.showLoading();

        fetch(`/api/files/${this.currentBrowser}?path=${encodeURIComponent(path)}`)
            .then(response => {
                if (response.status === 401) {
                    window.location.href = '/login';
                    return;
                }
                return response.json();
            })
            .then(data => {
                if (!data) return; // Handle redirect case
                // Handle direct response with files array
                this.currentFiles[this.currentBrowser] = data.files || [];
                this.applyFilters();
                this.renderFileList();
                this.updatePathDisplay();
            })
            .catch(error => {
                console.error('Error loading files:', error);
                this.showNotification('Failed to load files', 'error');
            })
            .finally(() => {
                this.hideLoading();
            });
    }

    renderFileList() {
        const fileList = document.getElementById('fileList');
        const files = this.filteredFiles[this.currentBrowser];

        if (files.length === 0) {
            fileList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-folder-open"></i>
                    <p>No files found</p>
                </div>
            `;
            return;
        }

        fileList.innerHTML = files.map(file => this.renderFileItem(file)).join('');

        // Add click handlers
        fileList.querySelectorAll('.file-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.handleFileClick(e, item);
            });

            // Add long press for selection
            let pressTimer;
            item.addEventListener('touchstart', (e) => {
                pressTimer = setTimeout(() => {
                    // Long press - toggle selection
                    const path = item.dataset.path;
                    const type = item.dataset.type;
                    if (type !== 'directory') {
                        this.toggleFileSelection(path);
                        // Provide haptic feedback if available
                        if (navigator.vibrate) {
                            navigator.vibrate(50);
                        }
                    }
                }, 500);
            });

            item.addEventListener('touchend', () => {
                clearTimeout(pressTimer);
            });

            item.addEventListener('touchcancel', () => {
                clearTimeout(pressTimer);
            });
        });

        // Add play button handlers
        fileList.querySelectorAll('.file-play-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const filePath = btn.dataset.filePath;
                this.playFile(filePath);
            });
        });
    }

    renderFileItem(file) {
        const isSelected = this.selectedFiles[this.currentBrowser].has(file.path);
        const iconClass = this.getFileIcon(file);
        const iconType = this.getFileIconType(file);
        const fileType = file.is_dir ? 'directory' : 'file';
        const isMediaFile = !file.is_dir && this.isMediaFile(file.name);

        return `
            <div class="file-item ${isSelected ? 'selected' : ''}" data-path="${file.path}" data-type="${fileType}">
                <div class="file-checkbox">
                    ${isSelected ? '<i class="fas fa-check"></i>' : ''}
                </div>
                <div class="file-icon ${iconType}">
                    <i class="${iconClass}"></i>
                </div>
                <div class="file-info">
                    <div class="file-name">${file.name}</div>
                    <div class="file-details">
                        ${file.is_dir ? 'Folder' : this.formatFileSize(file.size)}
                        ${file.modified ? `• ${this.formatDate(file.modified)}` : ''}
                    </div>
                </div>
                ${isMediaFile ? `
                    <div class="file-actions">
                        <button class="file-play-btn" data-file-path="${file.path}" title="Play">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                ` : ''}
            </div>
        `;
    }

    getFileIcon(file) {
        if (file.is_dir) return 'fas fa-folder';

        const ext = file.name.split('.').pop().toLowerCase();
        const audioExts = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a'];
        const videoExts = ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm'];

        if (audioExts.includes(ext)) return 'fas fa-music';
        if (videoExts.includes(ext)) return 'fas fa-video';
        return 'fas fa-file';
    }

    getFileIconType(file) {
        if (file.is_dir) return 'folder';

        const ext = file.name.split('.').pop().toLowerCase();
        const audioExts = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a'];
        const videoExts = ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm'];

        if (audioExts.includes(ext)) return 'audio';
        if (videoExts.includes(ext)) return 'video';
        return 'other';
    }

    handleFileClick(e, item) {
        const path = item.dataset.path;
        const type = item.dataset.type;

        if (e.target.closest('.file-checkbox')) {
            // Toggle selection
            this.toggleFileSelection(path);
            return;
        }

        if (type === 'directory') {
            // Navigate to directory
            this.navigateToPath(path);
        } else {
            // For media files: single click to play, checkbox to select
            const fileName = path.split('/').pop();
            if (this.isMediaFile(fileName)) {
                // Show a quick action menu or directly play
                this.showFileActionMenu(path, fileName);
            } else {
                // For non-media files, just toggle selection
                this.toggleFileSelection(path);
            }
        }
    }

    showFileActionMenu(filePath, fileName) {
        // For now, directly play the file
        // In the future, this could show a menu with "Play", "Add to Playlist", etc.
        this.playFile(filePath);
    }

    toggleFileSelection(path) {
        const selectedSet = this.selectedFiles[this.currentBrowser];
        if (selectedSet.has(path)) {
            selectedSet.delete(path);
        } else {
            selectedSet.add(path);
        }
        this.updateSelectionUI();
        this.renderFileList();
    }

    updateSelectionUI() {
        const selectedCount = this.selectedFiles[this.currentBrowser].size;
        const clearBtn = document.getElementById('clearSelectionBtn');
        const selectionActions = document.getElementById('selectionActions');
        const selectionCountSpan = document.getElementById('selectionCount');

        clearBtn.disabled = selectedCount === 0;

        if (selectedCount > 0) {
            selectionActions.style.display = 'block';
            selectionCountSpan.textContent = `${selectedCount} selected`;
        } else {
            selectionActions.style.display = 'none';
        }

        // Update add to playlist button
        const addToPlaylistBtn = document.getElementById('addToPlaylistBtn');
        const playlistSelect = document.getElementById('addToPlaylistSelect');
        addToPlaylistBtn.disabled = selectedCount === 0 || !playlistSelect.value;
    }

    selectAllFiles() {
        const mediaFiles = this.filteredFiles[this.currentBrowser].filter(file =>
            !file.is_dir && this.isMediaFile(file.name)
        );

        mediaFiles.forEach(file => {
            this.selectedFiles[this.currentBrowser].add(file.path);
        });

        this.updateSelectionUI();
        this.renderFileList();
    }

    clearSelection() {
        this.selectedFiles[this.currentBrowser].clear();
        this.updateSelectionUI();
        this.renderFileList();
    }

    isMediaFile(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        const mediaExts = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a', 'mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm'];
        return mediaExts.includes(ext);
    }

    applyFilters() {
        let files = [...this.currentFiles[this.currentBrowser]];

        // Apply search filter
        if (this.searchQuery) {
            files = files.filter(file =>
                file.name.toLowerCase().includes(this.searchQuery.toLowerCase())
            );
        }

        // Apply type filter
        switch (this.fileTypeFilter) {
            case 'media':
                files = files.filter(file =>
                    file.is_dir || this.isMediaFile(file.name)
                );
                break;
            case 'audio':
                files = files.filter(file => {
                    if (file.is_dir) return true;
                    const ext = file.name.split('.').pop().toLowerCase();
                    return ['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a'].includes(ext);
                });
                break;
            case 'video':
                files = files.filter(file => {
                    if (file.is_dir) return true;
                    const ext = file.name.split('.').pop().toLowerCase();
                    return ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm'].includes(ext);
                });
                break;
            case 'folders':
                files = files.filter(file => file.is_dir);
                break;
        }

        this.filteredFiles[this.currentBrowser] = files;
    }

    handleSearch(query) {
        this.searchQuery = query;
        this.applyFilters();
        this.renderFileList();
    }

    handleFilterChange(filter) {
        this.fileTypeFilter = filter;
        this.applyFilters();
        this.renderFileList();
    }

    // Navigation
    navigateToPath(path) {
        this.addToHistory(this.currentPath[this.currentBrowser]);
        this.currentPath[this.currentBrowser] = path;
        this.loadFiles();
    }

    navigateBack() {
        if (this.browserHistoryIndex[this.currentBrowser] > 0) {
            this.browserHistoryIndex[this.currentBrowser]--;
            const path = this.browserHistory[this.currentBrowser][this.browserHistoryIndex[this.currentBrowser]];
            this.currentPath[this.currentBrowser] = path;
            this.loadFiles();
        }
    }

    navigateUp() {
        const currentPath = this.currentPath[this.currentBrowser];
        if (this.currentBrowser === 'local') {
            // For local browser, don't allow navigation above data directory
            if (currentPath === 'data' || currentPath === '.') return;
        } else {
            if (currentPath === '/' || currentPath === '.') return;
        }

        const parentPath = currentPath.split('/').slice(0, -1).join('/') || (this.currentBrowser === 'local' ? 'data' : '/');
        this.navigateToPath(parentPath);
    }

    navigateHome() {
        let homePath;
        if (this.currentBrowser === 'local') {
            homePath = 'data';
        } else if (this.currentBrowser === 'webdav2') {
            homePath = '/dav';
        } else {
            homePath = '/';
        }
        this.navigateToPath(homePath);
    }

    refreshCurrentPath() {
        this.loadFiles();
    }

    addToHistory(path) {
        const history = this.browserHistory[this.currentBrowser];
        const index = this.browserHistoryIndex[this.currentBrowser];

        // Remove any forward history
        history.splice(index + 1);

        // Add new path
        history.push(path);
        this.browserHistoryIndex[this.currentBrowser] = history.length - 1;
    }

    updatePathDisplay() {
        const pathText = document.getElementById('currentPathText');
        pathText.textContent = this.currentPath[this.currentBrowser];
    }

    // Utility functions
    formatFileSize(bytes) {
        if (!bytes) return '';
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString();
    }

    // API calls
    async vlcControl(action) {
        try {
            const response = await fetch(`/api/vlc/${action}`, { method: 'POST' });
            const data = await response.json();
            if (!data.success) {
                this.showNotification(data.error || `Failed to ${action}`, 'error');
            }
        } catch (error) {
            console.error(`Error ${action}:`, error);
            this.showNotification(`Failed to ${action}`, 'error');
        }
    }

    async setVolume(volume) {
        try {
            const response = await fetch('/api/vlc/volume', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ volume: parseInt(volume) })
            });
            const data = await response.json();
            if (!data.success) {
                this.showNotification(data.error || 'Failed to set volume', 'error');
            }
        } catch (error) {
            console.error('Error setting volume:', error);
            this.showNotification('Failed to set volume', 'error');
        }
    }

    async toggleRepeat() {
        try {
            const response = await fetch('/api/vlc/repeat', { method: 'POST' });
            const data = await response.json();
            if (data.success) {
                this.updateRepeatButton(data.repeat);
                this.showNotification(`Repeat mode ${data.repeat ? 'enabled' : 'disabled'}`, 'success');
            } else {
                this.showNotification('Failed to toggle repeat mode', 'error');
            }
        } catch (error) {
            console.error('Error toggling repeat:', error);
            this.showNotification('Failed to toggle repeat mode', 'error');
        }
    }

    updateRepeatButton(isEnabled) {
        const repeatBtn = document.getElementById('repeatBtnCompact');
        if (repeatBtn) {
            if (isEnabled) {
                repeatBtn.classList.add('active');
                repeatBtn.style.background = 'linear-gradient(135deg, var(--primary-color), var(--primary-light))';
                repeatBtn.style.color = 'white';
            } else {
                repeatBtn.classList.remove('active');
                repeatBtn.style.background = '';
                repeatBtn.style.color = '';
            }
        }
    }

    async seekTo(position) {
        try {
            // Convert position (seconds) to percentage
            const percentage = this.totalDuration > 0 ? (position / this.totalDuration) * 100 : 0;

            const response = await fetch('/api/vlc/seek', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ percentage: percentage })
            });
            const data = await response.json();
            if (!data.success) {
                this.showNotification(data.error || 'Failed to seek', 'error');
            }
        } catch (error) {
            console.error('Error seeking:', error);
            this.showNotification('Failed to seek', 'error');
        }
    }

    async schedulerControl(action) {
        try {
            const response = await fetch(`/api/scheduler/${action}`, { method: 'POST' });
            const data = await response.json();
            if (data.success) {
                this.showNotification(`Scheduler ${action}ed successfully`, 'success');
            } else {
                this.showNotification(data.error || `Failed to ${action} scheduler`, 'error');
            }
        } catch (error) {
            console.error(`Error ${action} scheduler:`, error);
            this.showNotification(`Failed to ${action} scheduler`, 'error');
        }
    }

    // Data loading
    async loadInitialData() {
        await Promise.all([
            this.loadPlaylists(),
            this.loadSchedules(),
            this.loadSettings()
        ]);

        // Initialize file browser history
        this.initializeFileBrowserHistory();
    }

    initializeFileBrowserHistory() {
        // Initialize browser history for each browser type
        Object.keys(this.currentPath).forEach(browser => {
            if (this.browserHistory[browser].length === 0) {
                this.browserHistory[browser].push(this.currentPath[browser]);
                this.browserHistoryIndex[browser] = 0;
            }
        });
    }

    async loadPlaylists() {
        try {
            const response = await fetch('/api/playlists');
            if (response.status === 401) {
                window.location.href = '/login';
                return;
            }
            const data = await response.json();
            // Handle direct data response (not wrapped in success object)
            this.playlists = data || {};
            this.renderPlaylists();
            this.updatePlaylistSelects();
        } catch (error) {
            console.error('Error loading playlists:', error);
            this.showNotification('Failed to load playlists', 'error');
        }
    }

    async loadSchedules() {
        try {
            const response = await fetch('/api/schedules');
            if (response.status === 401) {
                window.location.href = '/login';
                return;
            }
            const data = await response.json();
            // Handle direct array response
            this.schedules = Array.isArray(data) ? data : [];
            this.renderSchedules();
        } catch (error) {
            console.error('Error loading schedules:', error);
            this.showNotification('Failed to load schedules', 'error');
        }
    }

    async loadSettings() {
        try {
            const response = await fetch('/api/settings');
            if (response.status === 401) {
                window.location.href = '/login';
                return;
            }
            const data = await response.json();
            if (data.success) {
                this.populateSettingsForms(data.settings || {});
            } else {
                console.error('Failed to load settings:', data.error);
                this.showNotification('Failed to load settings', 'error');
            }
        } catch (error) {
            console.error('Error loading settings:', error);
            this.showNotification('Failed to load settings', 'error');
        }
    }

    // Rendering
    renderPlaylists() {
        const grid = document.getElementById('playlistsGrid');
        const playlists = Object.values(this.playlists);

        if (playlists.length === 0) {
            grid.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-list"></i>
                    <p>No playlists found</p>
                    <button class="btn btn-primary" onclick="app.showPlaylistModal()">
                        <i class="fas fa-plus"></i> Create Playlist
                    </button>
                </div>
            `;
            return;
        }

        grid.innerHTML = playlists.map(playlist => `
            <div class="playlist-card" data-name="${playlist.name}">
                <div class="playlist-header" onclick="app.togglePlaylistExpand('${playlist.name}')">
                    <div>
                        <div class="playlist-name">${playlist.name}</div>
                        <div class="playlist-info">
                            <div>${playlist.items?.length || 0} items</div>
                            <div>Volume: ${playlist.volume}%</div>
                            ${playlist.shuffle ? '<div><i class="fas fa-random"></i> Shuffle</div>' : ''}
                            ${playlist.repeat ? '<div><i class="fas fa-redo"></i> Repeat</div>' : ''}
                        </div>
                    </div>
                    <div class="playlist-actions" onclick="event.stopPropagation()">
                        <button class="btn btn-primary small" onclick="app.playPlaylist('${playlist.name}')">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="btn btn-secondary small" onclick="app.showPlaylistModal(${JSON.stringify(playlist).replace(/"/g, '&quot;')})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-danger small" onclick="app.deletePlaylist('${playlist.name}')">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button class="btn btn-secondary small expand-btn" onclick="event.stopPropagation(); app.togglePlaylistExpand('${playlist.name}')">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                </div>
                <div class="playlist-items" id="playlist-items-${playlist.name}" style="display: none;">
                    ${this.renderPlaylistItems(playlist)}
                </div>
            </div>
        `).join('');
    }

    renderPlaylistItems(playlist) {
        if (!playlist.items || playlist.items.length === 0) {
            return '<div class="playlist-empty">No items in this playlist</div>';
        }

        return playlist.items.map((item, index) => `
            <div class="playlist-item" onclick="app.playPlaylistItem('${playlist.name}', ${index})">
                <div class="playlist-item-icon">
                    <i class="fas fa-music"></i>
                </div>
                <div class="playlist-item-info">
                    <div class="playlist-item-name">${this.decodeUnicode(item.name || item.path?.split('/').pop() || 'Unknown')}</div>
                    <div class="playlist-item-path">${item.path || ''}</div>
                </div>
                <div class="playlist-item-actions">
                    <button class="btn btn-primary small" onclick="event.stopPropagation(); app.playPlaylistItem('${playlist.name}', ${index})">
                        <i class="fas fa-play"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    togglePlaylistExpand(playlistName) {
        const itemsContainer = document.getElementById(`playlist-items-${playlistName}`);
        const expandBtn = document.querySelector(`[data-name="${playlistName}"] .expand-btn i`);

        if (itemsContainer.style.display === 'none') {
            itemsContainer.style.display = 'block';
            if (expandBtn) expandBtn.className = 'fas fa-chevron-up';
        } else {
            itemsContainer.style.display = 'none';
            if (expandBtn) expandBtn.className = 'fas fa-chevron-down';
        }
    }

    async playPlaylistItem(playlistName, itemIndex) {
        try {
            const response = await fetch(`/api/playlists/${encodeURIComponent(playlistName)}/play-item`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ index: itemIndex })
            });

            const data = await response.json();
            if (data.success) {
                this.showNotification(`Playing item from playlist: ${playlistName}`, 'success');
            } else {
                this.showNotification(data.error || 'Failed to play playlist item', 'error');
            }
        } catch (error) {
            console.error('Error playing playlist item:', error);
            this.showNotification('Failed to play playlist item', 'error');
        }
    }

    renderSchedules() {
        const list = document.getElementById('schedulesList');

        if (this.schedules.length === 0) {
            list.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-calendar"></i>
                    <p>No schedules found</p>
                    <button class="btn btn-primary" onclick="app.showScheduleModal()">
                        <i class="fas fa-plus"></i> Create Schedule
                    </button>
                </div>
            `;
            return;
        }

        list.innerHTML = this.schedules.map(schedule => `
            <div class="schedule-card ${!schedule.enabled ? 'disabled' : ''}" data-id="${schedule.id || ''}">
                <div class="schedule-header">
                    <div>
                        <div class="schedule-name">${schedule.name || 'Unnamed Schedule'}</div>
                        <div class="schedule-time">${schedule.start_time || '00:00'} - ${schedule.end_time || '00:00'}</div>
                    </div>
                    <div class="schedule-actions">
                        <button class="btn btn-secondary small" onclick="app.showScheduleModal(${JSON.stringify(schedule).replace(/"/g, '&quot;')})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-danger small" onclick="app.deleteSchedule('${schedule.id || ''}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="schedule-details">
                    <div class="schedule-detail">
                        <span class="label">Playlist:</span>
                        <span class="value">${schedule.playlist_name || 'None'}</span>
                    </div>
                    <div class="schedule-detail">
                        <span class="label">Volume:</span>
                        <span class="value">${schedule.volume || 70}%</span>
                    </div>
                    <div class="schedule-detail">
                        <span class="label">Days:</span>
                        <div class="schedule-days">
                            ${this.renderScheduleDays(schedule.days)}
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    renderScheduleDays(days) {
        const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
        const daysArray = Array.isArray(days) ? days : [];
        return dayNames.map((day, index) =>
            `<span class="schedule-day ${daysArray.includes(index) ? 'active' : ''}">${day}</span>`
        ).join('');
    }

    // Notifications
    showNotification(message, type = 'info', title = '') {
        const container = document.getElementById('notifications');
        const id = 'notification-' + Date.now();

        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.id = id;

        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        notification.innerHTML = `
            <div class="notification-icon">
                <i class="${iconMap[type]}"></i>
            </div>
            <div class="notification-content">
                ${title ? `<div class="notification-title">${title}</div>` : ''}
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close" onclick="app.closeNotification('${id}')">
                <i class="fas fa-times"></i>
            </button>
        `;

        container.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            this.closeNotification(id);
        }, 5000);
    }

    closeNotification(id) {
        const notification = document.getElementById(id);
        if (notification) {
            notification.remove();
        }
    }

    // Loading
    showLoading() {
        document.getElementById('loadingOverlay').style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    // Status updates
    startStatusUpdates() {
        this.updateStatus();
        setInterval(() => this.updateStatus(), 2000);
    }

    async updateStatus() {
        try {
            const response = await fetch('/api/status');
            if (response.status === 401) {
                window.location.href = '/login';
                return;
            }
            const data = await response.json();
            // Handle direct status object response
            this.status = data || {};
            this.updateUI();
        } catch (error) {
            console.error('Error updating status:', error);
        }
    }

    updateUI() {
        // Update media info
        if (this.status && this.status.vlc && this.status.vlc.media_info && Object.keys(this.status.vlc.media_info).length > 0) {
            const mediaInfo = this.status.vlc.media_info;

            // Decode Unicode characters for Chinese text
            const title = this.decodeUnicode(mediaInfo.title) || this.decodeUnicode(mediaInfo.filename) || 'Unknown';
            const artist = this.decodeUnicode(mediaInfo.artist) || '';

            document.getElementById('mediaTitle').textContent = title;
            document.getElementById('mediaDetails').textContent = artist;

            // Update compact media title
            const mediaTitleCompact = document.getElementById('mediaTitleCompact');
            if (mediaTitleCompact) {
                mediaTitleCompact.textContent = title;
            }

            if (this.status.vlc.current_playlist) {
                document.getElementById('currentPlaylistInfo').style.display = 'flex';
                document.getElementById('currentPlaylistName').textContent = this.decodeUnicode(this.status.vlc.current_playlist);
            } else {
                document.getElementById('currentPlaylistInfo').style.display = 'none';
            }
        } else {
            document.getElementById('mediaTitle').textContent = 'No media playing';
            document.getElementById('mediaDetails').textContent = '';
            document.getElementById('currentPlaylistInfo').style.display = 'none';

            // Update compact media title
            const mediaTitleCompact = document.getElementById('mediaTitleCompact');
            if (mediaTitleCompact) {
                mediaTitleCompact.textContent = 'No media playing';
            }
        }

        // Update playback state
        this.updatePlayPauseButton(this.status && this.status.vlc && this.status.vlc.state === 'playing');

        // Update progress - use correct data paths
        if (this.status && this.status.vlc && this.status.vlc.media_info) {
            const mediaInfo = this.status.vlc.media_info;
            const currentTime = mediaInfo.time ? mediaInfo.time / 1000 : 0; // Convert ms to seconds
            const totalTime = mediaInfo.duration ? mediaInfo.duration / 1000 : mediaInfo.length ? mediaInfo.length / 1000 : 0;

            if (totalTime > 0) {
                this.updateProgress(currentTime, totalTime);
            } else {
                // Reset progress if no valid duration
                this.updateProgress(0, 0);
            }
        } else {
            this.updateProgress(0, 0);
        }

        // Update volume
        if (this.status && this.status.vlc && this.status.vlc.volume !== undefined) {
            this.currentVolume = this.status.vlc.volume;
            const volumeSliderCompact = document.getElementById('volumeSliderCompact');
            if (volumeSliderCompact) {
                volumeSliderCompact.value = this.status.vlc.volume;
            }
            const volumeValueCompact = document.getElementById('volumeValueCompact');
            if (volumeValueCompact) {
                volumeValueCompact.textContent = this.status.vlc.volume + '%';
            }
            this.updateVolumeIcon(this.status.vlc.volume);
        }

        // Update repeat button
        if (this.status && this.status.vlc && this.status.vlc.repeat !== undefined) {
            this.updateRepeatButton(this.status.vlc.repeat);
        }

        // Update scheduler status
        if (this.status && this.status.scheduler) {
            const schedulerState = this.status.scheduler.state || 'stopped';

            // Update header scheduler status
            document.getElementById('schedulerState').textContent = schedulerState;

            // Update scheduler status badge
            const statusDot = document.getElementById('schedulerStatusDot');
            const statusText = document.getElementById('schedulerStatusText');

            if (statusDot && statusText) {
                statusText.textContent = schedulerState.charAt(0).toUpperCase() + schedulerState.slice(1);

                // Set status dot color based on state
                switch (schedulerState.toLowerCase()) {
                    case 'running':
                        statusDot.style.background = 'var(--success-color)';
                        break;
                    case 'paused':
                        statusDot.style.background = 'var(--warning-color)';
                        break;
                    case 'stopped':
                    default:
                        statusDot.style.background = 'var(--danger-color)';
                        break;
                }
            }

            // Format next schedule time
            let nextScheduleText = 'None';
            if (this.status.scheduler.next_scheduled) {
                const nextDate = new Date(this.status.scheduler.next_scheduled);
                nextScheduleText = nextDate.toLocaleString();
            }
            document.getElementById('nextSchedule').textContent = nextScheduleText;

            document.getElementById('currentSession').textContent = this.status.scheduler.current_session || 'None';
        }

        // Update system status
        const statusDot = document.querySelector('#systemStatus .status-dot');
        const statusText = document.querySelector('#systemStatus .status-text');
        if (statusDot && statusText) {
            if (this.status && this.status.system && this.status.system.running) {
                statusDot.style.background = 'var(--success-color)';
                statusText.textContent = 'Connected';
            } else {
                statusDot.style.background = 'var(--danger-color)';
                statusText.textContent = this.status ? 'Disconnected' : 'Connecting...';
            }
        }
    }

    // Helper function to decode Unicode characters
    decodeUnicode(str) {
        if (!str) return str;
        try {
            // If the string contains Unicode escape sequences like \u6b4c
            if (str.includes('\\u')) {
                return str.replace(/\\u[\dA-F]{4}/gi, function (match) {
                    return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16));
                });
            }
            return str;
        } catch (e) {
            return str;
        }
    }

    // Internationalization
    async initializeI18n() {
        try {
            const response = await fetch('/static/translations/en.json');
            this.translations.en = await response.json();

            const zhResponse = await fetch('/static/translations/zh.json');
            this.translations.zh = await zhResponse.json();

            this.currentLanguage = localStorage.getItem('language') || 'en';
            this.applyTranslations();
        } catch (error) {
            console.error('Error loading translations:', error);
        }
    }

    changeLanguage(lang) {
        this.currentLanguage = lang;
        localStorage.setItem('language', lang);
        this.applyTranslations();
    }

    applyTranslations() {
        const translations = this.translations[this.currentLanguage] || this.translations.en;

        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            const translation = this.getNestedTranslation(translations, key);
            if (translation) {
                element.textContent = translation;
            }
        });

        document.querySelectorAll('[data-i18n-title]').forEach(element => {
            const key = element.getAttribute('data-i18n-title');
            const translation = this.getNestedTranslation(translations, key);
            if (translation) {
                element.setAttribute('title', translation);
            }
        });
    }

    getNestedTranslation(obj, key) {
        return key.split('.').reduce((o, k) => o && o[k], obj);
    }

    // Settings management
    populateSettingsForms(settings) {
        if (settings.webdav) {
            document.getElementById('webdavUrl').value = settings.webdav.url || '';
            document.getElementById('webdavUsername').value = settings.webdav.username || '';
            document.getElementById('webdavEnabled').checked = settings.webdav.enabled || false;

            // Update status display
            document.getElementById('webdavServerUrl').textContent = settings.webdav.url || 'Not configured';
            document.getElementById('webdavUsernameDisplay').textContent = settings.webdav.username || 'Not configured';

            const statusDot = document.getElementById('webdavStatusDot');
            const statusText = document.getElementById('webdavStatusText');
            if (settings.webdav.connected) {
                statusDot.style.background = 'var(--success-color)';
                statusText.textContent = 'Connected';
            } else {
                statusDot.style.background = 'var(--danger-color)';
                statusText.textContent = 'Disconnected';
            }
        }

        if (settings.webdav2) {
            document.getElementById('webdav2Url').value = settings.webdav2.url || '';
            document.getElementById('webdav2Username').value = settings.webdav2.username || '';
            document.getElementById('webdav2Enabled').checked = settings.webdav2.enabled || false;

            // Update status display
            document.getElementById('webdav2ServerUrl').textContent = settings.webdav2.url || 'Not configured';
            document.getElementById('webdav2UsernameDisplay').textContent = settings.webdav2.username || 'Not configured';

            const status = document.getElementById('webdav2Status');
            if (settings.webdav2.connected) {
                status.querySelector('.status-dot').style.background = 'var(--success-color)';
                status.querySelector('.status-text').textContent = 'Connected';
            } else {
                status.querySelector('.status-dot').style.background = 'var(--danger-color)';
                status.querySelector('.status-text').textContent = 'Disconnected';
            }
        }

        if (settings.system) {
            document.getElementById('vlcStatus').textContent = 'Running';
            document.getElementById('webPort').textContent = settings.system.web_port || '5000';
            document.getElementById('autoStart').textContent = settings.system.auto_start ? 'Enabled' : 'Disabled';
        }
    }

    clearPlaylistForm() {
        document.getElementById('playlistName').value = '';
        document.getElementById('playlistVolume').value = 70;
        document.getElementById('playlistVolumeValue').textContent = '70%';
        document.getElementById('playlistShuffle').checked = false;
        document.getElementById('playlistRepeat').checked = false;
        document.getElementById('playlistItems').innerHTML = '';
    }

    populatePlaylistForm(playlist) {
        document.getElementById('playlistName').value = playlist.name || '';
        document.getElementById('playlistVolume').value = playlist.volume || 70;
        document.getElementById('playlistVolumeValue').textContent = (playlist.volume || 70) + '%';
        document.getElementById('playlistShuffle').checked = playlist.shuffle || false;
        document.getElementById('playlistRepeat').checked = playlist.repeat || false;

        // Populate items
        const itemsContainer = document.getElementById('playlistItems');
        itemsContainer.innerHTML = '';
        if (playlist.items) {
            playlist.items.forEach(item => {
                this.addPlaylistItemToForm(item);
            });
        }
    }

    clearScheduleForm() {
        document.getElementById('scheduleName').value = '';
        document.getElementById('scheduleStartTime').value = '';
        document.getElementById('scheduleEndTime').value = '';
        document.getElementById('schedulePlaylist').value = '';
        document.getElementById('scheduleVolume').value = 70;
        document.getElementById('scheduleVolumeValue').textContent = '70%';
        document.getElementById('scheduleEnabled').checked = true;

        // Clear all day checkboxes
        document.querySelectorAll('input[name="days"]').forEach(cb => cb.checked = false);
    }

    populateScheduleForm(schedule) {
        document.getElementById('scheduleName').value = schedule.name || '';
        document.getElementById('scheduleStartTime').value = schedule.start_time || '';
        document.getElementById('scheduleEndTime').value = schedule.end_time || '';
        document.getElementById('schedulePlaylist').value = schedule.playlist_name || '';
        document.getElementById('scheduleVolume').value = schedule.volume || 70;
        document.getElementById('scheduleVolumeValue').textContent = (schedule.volume || 70) + '%';
        document.getElementById('scheduleEnabled').checked = schedule.enabled !== false;

        // Set day checkboxes
        document.querySelectorAll('input[name="days"]').forEach(cb => {
            cb.checked = schedule.days && schedule.days.includes(parseInt(cb.value));
        });
    }

    loadPlaylistsForSchedule() {
        const select = document.getElementById('schedulePlaylist');
        select.innerHTML = '<option value="">Select a playlist...</option>';

        Object.values(this.playlists).forEach(playlist => {
            const option = document.createElement('option');
            option.value = playlist.name;
            option.textContent = playlist.name;
            select.appendChild(option);
        });
    }

    updatePlaylistSelects() {
        const select = document.getElementById('addToPlaylistSelect');
        select.innerHTML = '<option value="">Add to Playlist...</option>';

        Object.values(this.playlists).forEach(playlist => {
            const option = document.createElement('option');
            option.value = playlist.name;
            option.textContent = playlist.name;
            select.appendChild(option);
        });
    }

    updateSelectedFilesPreview() {
        const preview = document.getElementById('selectedFilesPreview');
        const count = document.getElementById('selectedFileCount');
        const selectedFiles = Array.from(this.selectedFiles[this.currentBrowser]);

        count.textContent = selectedFiles.length;

        if (selectedFiles.length === 0) {
            preview.innerHTML = '<p>No files selected</p>';
            return;
        }

        preview.innerHTML = selectedFiles.slice(0, 5).map(path => {
            const fileName = path.split('/').pop();
            return `<div class="selected-file-item">${fileName}</div>`;
        }).join('');

        if (selectedFiles.length > 5) {
            preview.innerHTML += `<div class="selected-file-more">... and ${selectedFiles.length - 5} more</div>`;
        }
    }

    addPlaylistItemToForm(item = null) {
        const container = document.getElementById('playlistItems');
        const itemDiv = document.createElement('div');
        itemDiv.className = 'playlist-item-form';

        itemDiv.innerHTML = `
            <div class="form-group">
                <input type="text" placeholder="File path or URL" value="${item?.path || ''}" class="playlist-item-path">
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-danger small" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;

        container.appendChild(itemDiv);
    }

    // API calls for forms
    async savePlaylist() {
        const form = document.getElementById('playlistForm');
        const formData = new FormData(form);

        const playlist = {
            name: formData.get('name'),
            volume: parseInt(formData.get('volume')),
            shuffle: formData.has('shuffle'),
            repeat: formData.has('repeat'),
            items: []
        };

        // Collect playlist items
        document.querySelectorAll('.playlist-item-path').forEach(input => {
            if (input.value.trim()) {
                playlist.items.push({ path: input.value.trim() });
            }
        });

        try {
            const response = await fetch('/api/playlists', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(playlist)
            });

            const data = await response.json();
            if (data.success) {
                this.showNotification('Playlist saved successfully', 'success');
                this.closeActiveModal();
                this.loadPlaylists();
            } else {
                this.showNotification(data.error || 'Failed to save playlist', 'error');
            }
        } catch (error) {
            console.error('Error saving playlist:', error);
            this.showNotification('Failed to save playlist', 'error');
        }
    }

    async createQuickPlaylist() {
        const form = document.getElementById('quickPlaylistForm');
        const formData = new FormData(form);

        const playlist = {
            name: formData.get('name'),
            volume: parseInt(formData.get('volume')),
            shuffle: formData.has('shuffle'),
            repeat: formData.has('repeat'),
            items: Array.from(this.selectedFiles[this.currentBrowser]).map(path => ({ path }))
        };

        try {
            const response = await fetch('/api/playlists', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(playlist)
            });

            const data = await response.json();
            if (data.success) {
                this.showNotification('Playlist created successfully', 'success');
                this.closeActiveModal();
                this.clearSelection();
                this.loadPlaylists();
            } else {
                this.showNotification(data.error || 'Failed to create playlist', 'error');
            }
        } catch (error) {
            console.error('Error creating playlist:', error);
            this.showNotification('Failed to create playlist', 'error');
        }
    }

    async saveSchedule() {
        const form = document.getElementById('scheduleForm');
        const formData = new FormData(form);

        const days = [];
        document.querySelectorAll('input[name="days"]:checked').forEach(cb => {
            days.push(parseInt(cb.value));
        });

        const schedule = {
            name: formData.get('name'),
            start_time: formData.get('start_time'),
            end_time: formData.get('end_time'),
            playlist_name: formData.get('playlist_name'),
            volume: parseInt(formData.get('volume')),
            days: days,
            enabled: formData.has('enabled')
        };

        try {
            const response = await fetch('/api/schedules', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(schedule)
            });

            const data = await response.json();
            if (data.success) {
                this.showNotification('Schedule saved successfully', 'success');
                this.closeActiveModal();
                this.loadSchedules();
            } else {
                this.showNotification(data.error || 'Failed to save schedule', 'error');
            }
        } catch (error) {
            console.error('Error saving schedule:', error);
            this.showNotification('Failed to save schedule', 'error');
        }
    }

    async saveWebDAVSettings(e) {
        e.preventDefault();
        const form = e.target;
        const formData = new FormData(form);

        const settings = {
            url: formData.get('url'),
            username: formData.get('username'),
            password: formData.get('password'),
            enabled: formData.has('enabled')
        };

        try {
            const response = await fetch('/api/webdav/config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(settings)
            });

            const data = await response.json();
            if (data.success) {
                this.showNotification('WebDAV settings saved successfully', 'success');
                this.loadSettings();
            } else {
                this.showNotification(data.error || 'Failed to save WebDAV settings', 'error');
            }
        } catch (error) {
            console.error('Error saving WebDAV settings:', error);
            this.showNotification('Failed to save WebDAV settings', 'error');
        }
    }

    async saveWebDAV2Settings(e) {
        e.preventDefault();
        const form = e.target;
        const formData = new FormData(form);

        const settings = {
            url: formData.get('url'),
            username: formData.get('username'),
            password: formData.get('password'),
            enabled: formData.has('enabled')
        };

        try {
            const response = await fetch('/api/webdav2/config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(settings)
            });

            const data = await response.json();
            if (data.success) {
                this.showNotification('WebDAV2 settings saved successfully', 'success');
                this.loadSettings();
            } else {
                this.showNotification(data.error || 'Failed to save WebDAV2 settings', 'error');
            }
        } catch (error) {
            console.error('Error saving WebDAV2 settings:', error);
            this.showNotification('Failed to save WebDAV2 settings', 'error');
        }
    }

    async reconnectWebDAV() {
        try {
            this.showNotification('Reconnecting WebDAV...', 'info');
            const response = await fetch('/api/webdav/reconnect', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const data = await response.json();
            if (data.success) {
                this.showNotification('WebDAV reconnected successfully', 'success');
                // Refresh settings to update status
                this.loadSettings();
                // Refresh file list if currently browsing WebDAV
                if (this.currentBrowser === 'webdav') {
                    this.loadFiles();
                }
            } else {
                this.showNotification(data.message || 'Failed to reconnect WebDAV', 'error');
            }
        } catch (error) {
            console.error('Error reconnecting WebDAV:', error);
            this.showNotification('Failed to reconnect WebDAV', 'error');
        }
    }

    async reconnectWebDAV2() {
        try {
            this.showNotification('Reconnecting WebDAV2...', 'info');
            const response = await fetch('/api/webdav2/reconnect', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const data = await response.json();
            if (data.success) {
                this.showNotification('WebDAV2 reconnected successfully', 'success');
                // Refresh settings to update status
                this.loadSettings();
                // Refresh file list if currently browsing WebDAV2
                if (this.currentBrowser === 'webdav2') {
                    this.loadFiles();
                }
            } else {
                this.showNotification(data.message || 'Failed to reconnect WebDAV2', 'error');
            }
        } catch (error) {
            console.error('Error reconnecting WebDAV2:', error);
            this.showNotification('Failed to reconnect WebDAV2', 'error');
        }
    }

    async playPlaylist(playlistName) {
        try {
            const response = await fetch(`/api/playlists/${encodeURIComponent(playlistName)}/play`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const data = await response.json();
            if (data.success) {
                this.showNotification(`Playing playlist: ${playlistName}`, 'success');
            } else {
                this.showNotification(data.error || 'Failed to play playlist', 'error');
            }
        } catch (error) {
            console.error('Error playing playlist:', error);
            this.showNotification('Failed to play playlist', 'error');
        }
    }

    async deletePlaylist(playlistName) {
        if (!confirm(`Are you sure you want to delete playlist "${playlistName}"?`)) {
            return;
        }

        try {
            const response = await fetch(`/api/playlists/${encodeURIComponent(playlistName)}`, {
                method: 'DELETE'
            });

            const data = await response.json();
            if (data.success) {
                this.showNotification('Playlist deleted successfully', 'success');
                this.loadPlaylists();
            } else {
                this.showNotification(data.error || 'Failed to delete playlist', 'error');
            }
        } catch (error) {
            console.error('Error deleting playlist:', error);
            this.showNotification('Failed to delete playlist', 'error');
        }
    }

    async deleteSchedule(scheduleId) {
        if (!confirm('Are you sure you want to delete this schedule?')) {
            return;
        }

        try {
            const response = await fetch(`/api/schedules/${scheduleId}`, {
                method: 'DELETE'
            });

            const data = await response.json();
            if (data.success) {
                this.showNotification('Schedule deleted successfully', 'success');
                this.loadSchedules();
            } else {
                this.showNotification(data.error || 'Failed to delete schedule', 'error');
            }
        } catch (error) {
            console.error('Error deleting schedule:', error);
            this.showNotification('Failed to delete schedule', 'error');
        }
    }

    async addSelectedToPlaylist() {
        const playlistName = document.getElementById('addToPlaylistSelect').value;
        if (!playlistName) {
            this.showNotification('Please select a playlist', 'warning');
            return;
        }

        const selectedFiles = Array.from(this.selectedFiles[this.currentBrowser]);
        if (selectedFiles.length === 0) {
            this.showNotification('No files selected', 'warning');
            return;
        }

        try {
            const response = await fetch(`/api/playlists/${encodeURIComponent(playlistName)}/items`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ files: selectedFiles })
            });

            const data = await response.json();
            if (data.success) {
                this.showNotification(`Added ${selectedFiles.length} files to playlist`, 'success');
                this.clearSelection();
            } else {
                this.showNotification(data.error || 'Failed to add files to playlist', 'error');
            }
        } catch (error) {
            console.error('Error adding files to playlist:', error);
            this.showNotification('Failed to add files to playlist', 'error');
        }
    }

    performSearch() {
        const query = document.getElementById('fileSearchInput').value;
        this.handleSearch(query);
    }

    async playFile(filePath) {
        try {
            const isWebdav = this.currentBrowser === 'webdav' || this.currentBrowser === 'webdav2';
            const response = await fetch('/api/vlc/play-file', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    path: filePath,
                    is_webdav: isWebdav,
                    webdav_type: this.currentBrowser === 'webdav2' ? 'webdav2' : 'webdav'
                })
            });

            const data = await response.json();
            if (data.success) {
                const fileName = filePath.split('/').pop();
                this.showNotification(`Playing: ${fileName}`, 'success');
            } else {
                this.showNotification(data.error || 'Failed to play file', 'error');
            }
        } catch (error) {
            console.error('Error playing file:', error);
            this.showNotification('Failed to play file', 'error');
        }
    }

    // Logout
    logout() {
        if (confirm('Are you sure you want to logout?')) {
            window.location.href = '/logout';
        }
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new MobileVLCApp();
});
