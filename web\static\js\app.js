/**
 * VLC Automation System - Frontend JavaScript
 * Handles UI interactions and API communication
 */

class VLCApp {
    constructor() {
        this.currentTab = 'playlists';
        this.currentBrowser = 'local';
        this.playlists = {};
        this.schedules = [];
        this.status = {};

        // Internationalization
        this.currentLanguage = 'en';
        this.translations = {};

        // File browser state
        this.browserHistory = {
            local: [],
            webdav: [],
            webdav2: []
        };
        this.browserHistoryIndex = {
            local: -1,
            webdav: -1,
            webdav2: -1
        };
        this.currentPath = {
            local: '.',
            webdav: '/',
            webdav2: '/dav'
        };
        this.currentFiles = {
            local: [],
            webdav: [],
            webdav2: []
        };
        this.filteredFiles = {
            local: [],
            webdav: [],
            webdav2: []
        };
        this.currentView = 'list';
        this.searchQuery = '';
        this.fileTypeFilter = 'all';

        // File selection state
        this.selectedFiles = {
            local: new Set(),
            webdav: new Set(),
            webdav2: new Set()
        };

        this.init();
    }
    
    async init() {
        await this.initializeI18n();
        this.setupEventListeners();
        this.setupTabs();
        this.setupModals();
        this.loadInitialData();
        this.startStatusUpdates();
    }
    
    setupEventListeners() {
        // Media controls
        document.getElementById('playBtn').addEventListener('click', () => this.vlcControl('play'));
        document.getElementById('pauseBtn').addEventListener('click', () => this.vlcControl('pause'));
        document.getElementById('stopBtn').addEventListener('click', () => this.vlcControl('stop'));
        document.getElementById('nextBtn').addEventListener('click', () => this.vlcControl('next'));
        document.getElementById('prevBtn').addEventListener('click', () => this.vlcControl('previous'));
        document.getElementById('repeatBtn').addEventListener('click', () => this.toggleRepeat());
        
        // Volume control
        const volumeSlider = document.getElementById('volumeSlider');
        volumeSlider.addEventListener('input', (e) => {
            const volume = e.target.value;
            document.getElementById('volumeValue').textContent = volume + '%';
            this.setVolume(volume);
        });

        // Progress bar control
        this.setupProgressBar();

        // Scheduler controls
        document.getElementById('startSchedulerBtn').addEventListener('click', () => this.schedulerControl('start'));
        document.getElementById('pauseSchedulerBtn').addEventListener('click', () => this.schedulerControl('pause'));
        document.getElementById('stopSchedulerBtn').addEventListener('click', () => this.schedulerControl('stop'));
        
        // Playlist management
        document.getElementById('addPlaylistBtn').addEventListener('click', () => this.showPlaylistModal());
        document.getElementById('savePlaylistBtn').addEventListener('click', () => this.savePlaylist());
        document.getElementById('cancelPlaylistBtn').addEventListener('click', () => this.hidePlaylistModal());
        document.getElementById('closePlaylistModal').addEventListener('click', () => this.hidePlaylistModal());
        
        // Schedule management
        document.getElementById('addScheduleBtn').addEventListener('click', () => this.showScheduleModal());
        document.getElementById('saveScheduleBtn').addEventListener('click', () => this.saveSchedule());
        document.getElementById('cancelScheduleBtn').addEventListener('click', () => this.hideScheduleModal());
        document.getElementById('closeScheduleModal').addEventListener('click', () => this.hideScheduleModal());
        
        // File browser
        document.getElementById('browseLocalBtn').addEventListener('click', () => this.browseLocal());
        document.getElementById('browseWebdavBtn').addEventListener('click', () => this.browseWebdav());
        document.getElementById('browseWebdav2Btn').addEventListener('click', () => this.browseWebdav2());

        // File browser navigation
        document.getElementById('backBtn').addEventListener('click', () => this.navigateBack());
        document.getElementById('forwardBtn').addEventListener('click', () => this.navigateForward());
        document.getElementById('upBtn').addEventListener('click', () => this.navigateUp());
        document.getElementById('homeBtn').addEventListener('click', () => this.navigateHome());
        document.getElementById('refreshBtn').addEventListener('click', () => this.refreshCurrentDirectory());

        // File browser search and filter
        document.getElementById('searchBtn').addEventListener('click', () => this.searchFiles());
        document.getElementById('fileSearchInput').addEventListener('input', (e) => {
            this.searchQuery = e.target.value;
            this.applyFilters();
        });
        document.getElementById('fileTypeFilter').addEventListener('change', (e) => {
            this.fileTypeFilter = e.target.value;
            this.applyFilters();
        });

        // View options
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const view = e.currentTarget.dataset.view;
                this.setView(view);
            });
        });

        // Path input enter key
        document.getElementById('localPath').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.browseLocal();
        });
        document.getElementById('webdavPath').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.browseWebdav();
        });
        document.getElementById('webdav2Path').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.browseWebdav2();
        });
        
        // WebDAV settings
        document.getElementById('webdavForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveWebdavConfig();
        });
        document.getElementById('testWebdavBtn').addEventListener('click', () => this.testWebdavConnection());
        document.getElementById('reconnectWebdavBtn').addEventListener('click', () => this.reconnectWebdav());

        // WebDAV2 settings
        document.getElementById('webdav2Form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveWebdav2Config();
        });
        document.getElementById('testWebdav2Btn').addEventListener('click', () => this.testWebdav2Connection());
        document.getElementById('reconnectWebdav2Btn').addEventListener('click', () => this.reconnectWebdav2());

        // Playlist controls
        document.getElementById('createPlaylistFromSelection').addEventListener('click', () => this.showQuickPlaylistModal());
        document.getElementById('addToPlaylistBtn').addEventListener('click', () => this.addSelectedFilesToPlaylist());
        document.getElementById('selectAllBtn').addEventListener('click', () => this.selectAllMediaFiles());
        document.getElementById('clearSelectionBtn').addEventListener('click', () => this.clearFileSelection());

        // Quick playlist modal
        document.getElementById('createQuickPlaylistBtn').addEventListener('click', () => this.createPlaylistFromSelection());
        document.getElementById('cancelQuickPlaylistBtn').addEventListener('click', () => this.hideQuickPlaylistModal());
        document.getElementById('closeQuickPlaylistModal').addEventListener('click', () => this.hideQuickPlaylistModal());

        // Volume slider for quick playlist
        document.getElementById('quickPlaylistVolume').addEventListener('input', (e) => {
            document.getElementById('quickPlaylistVolumeValue').textContent = e.target.value + '%';
        });
        
        // Playlist form
        document.getElementById('addPlaylistItem').addEventListener('click', () => this.addPlaylistItem());
        
        // Volume sliders in modals
        document.getElementById('playlistVolume').addEventListener('input', (e) => {
            document.getElementById('playlistVolumeValue').textContent = e.target.value + '%';
        });
        document.getElementById('scheduleVolume').addEventListener('input', (e) => {
            document.getElementById('scheduleVolumeValue').textContent = e.target.value + '%';
        });
    }
    
    setupTabs() {
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabPanes = document.querySelectorAll('.tab-pane');
        
        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const tabName = btn.dataset.tab;
                
                // Update active tab button
                tabBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                // Update active tab pane
                tabPanes.forEach(pane => pane.classList.remove('active'));
                document.getElementById(tabName).classList.add('active');
                
                this.currentTab = tabName;
                this.onTabChange(tabName);
            });
        });
        
        // Browser tabs
        const browserTabs = document.querySelectorAll('.browser-tab');
        const browserPanes = document.querySelectorAll('.browser-pane');
        
        browserTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const browserName = tab.dataset.browser;

                browserTabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');

                browserPanes.forEach(pane => pane.classList.remove('active'));
                document.getElementById(browserName + 'Browser').classList.add('active');

                this.currentBrowser = browserName;

                // Load files for the selected browser
                if (browserName === 'local') {
                    this.browseLocal();
                } else if (browserName === 'webdav2') {
                    this.browseWebdav2();
                } else if (browserName === 'webdav') {
                    this.browseWebdav();
                }
            });
        });
    }
    
    setupModals() {
        // Close modals when clicking outside
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.classList.remove('active');
                }
            });
        });
    }
    
    async loadInitialData() {
        try {
            await Promise.all([
                this.loadPlaylists(),
                this.loadSchedules(),
                this.loadWebdavConfig(),
                this.loadWebdav2Config(),
                this.updateStatus()
            ]);
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.showNotification('Error loading application data', 'error');
        }
    }
    
    startStatusUpdates() {
        // Update status every 2 seconds
        setInterval(() => {
            this.updateStatus();
        }, 2000);

        // Initialize file browser with default local files
        this.browseLocal();
    }
    
    onTabChange(tabName) {
        switch (tabName) {
            case 'playlists':
                this.loadPlaylists();
                break;
            case 'schedules':
                this.loadSchedules();
                break;
            case 'files':
                if (this.currentBrowser === 'local') {
                    this.browseLocal();
                } else if (this.currentBrowser === 'webdav2') {
                    this.browseWebdav2();
                } else {
                    this.browseWebdav();
                }
                break;
            case 'settings':
                this.loadWebdavConfig();
                this.loadWebdav2Config();
                break;
        }
    }
    
    // API Methods
    async apiCall(endpoint, options = {}) {
        try {
            const response = await fetch(`/api${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error(`API call failed: ${endpoint}`, error);
            throw error;
        }
    }
    
    // VLC Control Methods
    async vlcControl(action) {
        try {
            const result = await this.apiCall(`/vlc/${action}`, { method: 'POST' });
            if (result.success) {
                this.showNotification(`VLC ${action} successful`, 'success');
            } else {
                this.showNotification(`VLC ${action} failed`, 'error');
            }
        } catch (error) {
            this.showNotification(`Error controlling VLC: ${error.message}`, 'error');
        }
    }
    
    async setVolume(volume) {
        try {
            const result = await this.apiCall('/vlc/volume', {
                method: 'POST',
                body: JSON.stringify({ volume: parseInt(volume) })
            });

            if (!result.success) {
                this.showNotification('Failed to set volume', 'error');
            }
        } catch (error) {
            this.showNotification(`Error setting volume: ${error.message}`, 'error');
        }
    }

    async toggleRepeat() {
        try {
            const result = await this.apiCall('/vlc/repeat', { method: 'POST' });
            if (result.success) {
                this.updateRepeatButton(result.repeat);
                this.showNotification(`Repeat mode ${result.repeat ? 'enabled' : 'disabled'}`, 'success');
            } else {
                this.showNotification('Failed to toggle repeat mode', 'error');
            }
        } catch (error) {
            this.showNotification(`Error toggling repeat: ${error.message}`, 'error');
        }
    }

    updateRepeatButton(isEnabled) {
        const repeatBtn = document.getElementById('repeatBtn');
        if (isEnabled) {
            repeatBtn.classList.add('active');
            repeatBtn.style.color = '#007bff';
        } else {
            repeatBtn.classList.remove('active');
            repeatBtn.style.color = '';
        }
    }

    // Progress Bar Methods
    setupProgressBar() {
        const progressBar = document.getElementById('progressBar');
        const progressHandle = document.getElementById('progressHandle');
        let isDragging = false;

        // Mouse events for progress bar
        progressBar.addEventListener('mousedown', (e) => {
            if (e.target === progressBar || e.target === document.getElementById('progressFill')) {
                this.seekToPosition(e);
            }
        });

        // Handle dragging
        progressHandle.addEventListener('mousedown', (e) => {
            e.preventDefault();
            isDragging = true;
            document.addEventListener('mousemove', this.handleProgressDrag.bind(this));
            document.addEventListener('mouseup', () => {
                isDragging = false;
                document.removeEventListener('mousemove', this.handleProgressDrag.bind(this));
            });
        });

        // Click on progress bar to seek
        progressBar.addEventListener('click', (e) => {
            if (!isDragging) {
                this.seekToPosition(e);
            }
        });
    }

    handleProgressDrag(e) {
        const progressBar = document.getElementById('progressBar');
        const rect = progressBar.getBoundingClientRect();
        const percentage = Math.max(0, Math.min(100, ((e.clientX - rect.left) / rect.width) * 100));

        // Update visual progress immediately for smooth dragging
        this.updateProgressDisplay(percentage);

        // Seek to position (throttled to avoid too many API calls)
        if (!this.seekThrottle) {
            this.seekThrottle = setTimeout(() => {
                this.seekToPercentage(percentage);
                this.seekThrottle = null;
            }, 100);
        }
    }

    seekToPosition(e) {
        const progressBar = document.getElementById('progressBar');
        const rect = progressBar.getBoundingClientRect();
        const percentage = ((e.clientX - rect.left) / rect.width) * 100;
        this.seekToPercentage(Math.max(0, Math.min(100, percentage)));
    }

    async seekToPercentage(percentage) {
        try {
            const result = await this.apiCall('/vlc/seek', {
                method: 'POST',
                body: JSON.stringify({ percentage: percentage })
            });

            if (!result.success) {
                console.warn('Seek failed:', result.error);
            }
        } catch (error) {
            console.error('Error seeking:', error);
        }
    }

    updateProgressDisplay(percentage, currentTime = null, totalTime = null) {
        const progressFill = document.getElementById('progressFill');
        const progressHandle = document.getElementById('progressHandle');

        if (progressFill) {
            progressFill.style.width = percentage + '%';
        }

        if (progressHandle) {
            progressHandle.style.left = percentage + '%';
        }

        // Update time display if provided
        if (currentTime !== null) {
            document.getElementById('currentTime').textContent = this.formatTime(currentTime);
        }

        if (totalTime !== null) {
            document.getElementById('totalTime').textContent = this.formatTime(totalTime);
        }
    }

    // Scheduler Control Methods
    async schedulerControl(action) {
        try {
            const result = await this.apiCall(`/scheduler/${action}`, { method: 'POST' });
            if (result.success) {
                this.showNotification(`Scheduler ${action} successful`, 'success');
                this.updateStatus();
            } else {
                this.showNotification(`Scheduler ${action} failed`, 'error');
            }
        } catch (error) {
            this.showNotification(`Error controlling scheduler: ${error.message}`, 'error');
        }
    }
    
    // Status Update Methods
    async updateStatus() {
        try {
            this.status = await this.apiCall('/status');
            this.updateStatusDisplay();
        } catch (error) {
            console.error('Error updating status:', error);
            this.updateConnectionStatus(false);
        }
    }
    
    updateStatusDisplay() {
        // Update system status indicator
        const statusIndicator = document.getElementById('systemStatus');
        const statusDot = statusIndicator.querySelector('.status-dot');
        const statusText = statusIndicator.querySelector('.status-text');
        
        if (this.status.system?.running) {
            statusDot.className = 'status-dot';
            statusText.textContent = 'System Running';
        } else {
            statusDot.className = 'status-dot error';
            statusText.textContent = 'System Stopped';
        }
        
        // Update VLC status
        if (this.status.vlc) {
            const vlcState = this.status.vlc.state;
            const volume = this.status.vlc.volume;
            const mediaInfo = this.status.vlc.media_info;
            const repeatMode = this.status.vlc.repeat;

            // Update volume slider
            document.getElementById('volumeSlider').value = volume;
            document.getElementById('volumeValue').textContent = volume + '%';

            // Update repeat button
            this.updateRepeatButton(repeatMode);

            // Update current media display
            this.updateCurrentMediaDisplay(mediaInfo, vlcState);
        }
        
        // Update scheduler status
        if (this.status.scheduler) {
            document.getElementById('schedulerState').textContent = this.status.scheduler.state;
            document.getElementById('nextSchedule').textContent =
                this.status.scheduler.next_scheduled || 'None';
            document.getElementById('currentSession').textContent =
                this.status.scheduler.current_session?.schedule_name || 'None';
        }

        // Update WebDAV status displays
        if (this.status.webdav) {
            this.updateWebdavStatusDisplay(this.status.webdav);
        }
        if (this.status.webdav2) {
            this.updateWebdav2StatusDisplay(this.status.webdav2);
        }
    }
    
    updateCurrentMediaDisplay(mediaInfo, state) {
        const currentMedia = document.getElementById('currentMedia');
        const mediaTitle = currentMedia.querySelector('.media-title');
        const mediaDetails = currentMedia.querySelector('.media-details');
        const playlistInfo = document.getElementById('currentPlaylistInfo');
        const playlistName = document.getElementById('currentPlaylistName');

        if (mediaInfo && mediaInfo.title && mediaInfo.title !== 'Unknown') {
            const decodedTitle = this.decodeFileName(mediaInfo.title);
            mediaTitle.textContent = decodedTitle;

            const details = [];
            if (mediaInfo.artist && mediaInfo.artist !== 'Unknown') {
                details.push(`Artist: ${mediaInfo.artist}`);
            }
            if (mediaInfo.album && mediaInfo.album !== 'Unknown') {
                details.push(`Album: ${mediaInfo.album}`);
            }
            details.push(`State: ${state}`);

            if (mediaInfo.length > 0) {
                const duration = this.formatTime(mediaInfo.length / 1000);
                const position = this.formatTime(mediaInfo.time / 1000);
                details.push(`Time: ${position} / ${duration}`);
            }

            mediaDetails.textContent = details.join(' • ');
        } else {
            mediaTitle.textContent = state === 'playing' ? 'Playing...' : 'No media playing';
            mediaDetails.textContent = `State: ${state}`;
        }

        // Update progress bar
        if (mediaInfo && mediaInfo.length > 0) {
            const percentage = (mediaInfo.time / mediaInfo.length) * 100;
            const currentTime = mediaInfo.time / 1000; // Convert to seconds
            const totalTime = mediaInfo.length / 1000; // Convert to seconds
            this.updateProgressDisplay(percentage, currentTime, totalTime);
        } else {
            // Reset progress bar when no media or no duration info
            this.updateProgressDisplay(0, 0, 0);
        }

        // Update current playlist display
        if (this.status.vlc && this.status.vlc.current_playlist) {
            playlistInfo.style.display = 'block';
            playlistName.textContent = this.status.vlc.current_playlist;
        } else {
            playlistInfo.style.display = 'none';
        }
    }
    
    updateConnectionStatus(connected) {
        const statusDot = document.querySelector('.status-dot');
        const statusText = document.querySelector('.status-text');
        
        if (connected) {
            statusDot.className = 'status-dot';
            statusText.textContent = 'Connected';
        } else {
            statusDot.className = 'status-dot error';
            statusText.textContent = 'Connection Error';
        }
    }
    
    formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }

    // Playlist Management Methods
    async loadPlaylists() {
        try {
            this.playlists = await this.apiCall('/playlists');
            this.renderPlaylists();
            this.updatePlaylistDropdown(); // Update the file browser playlist dropdown
        } catch (error) {
            this.showNotification('Error loading playlists', 'error');
        }
    }

    renderPlaylists() {
        const grid = document.getElementById('playlistsGrid');
        grid.innerHTML = '';

        Object.values(this.playlists).forEach(playlist => {
            const card = document.createElement('div');
            card.className = 'playlist-card';

            // Count local vs WebDAV files
            const localFiles = playlist.items.filter(item => !item.is_webdav);
            const webdavFiles = playlist.items.filter(item => item.is_webdav);

            // Generate file list HTML
            const fileListHtml = this.generatePlaylistFileList(playlist.items);

            card.innerHTML = `
                <div class="playlist-header">
                    <div class="playlist-title">${playlist.name}</div>
                    <div class="playlist-actions">
                        <button class="btn btn-sm btn-secondary" onclick="app.editPlaylist('${playlist.name}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="app.deletePlaylist('${playlist.name}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="playlist-info">
                    <div class="playlist-stats">
                        <span class="total-items">${playlist.items.length} items</span>
                        <span class="local-files">${localFiles.length} local</span>
                        ${webdavFiles.length > 0 ? `<span class="webdav-files">${webdavFiles.length} network</span>` : ''}
                        <span class="volume">Volume: ${playlist.volume}%</span>
                        ${playlist.shuffle ? '<span class="shuffle">Shuffle</span>' : ''}
                        ${playlist.repeat ? '<span class="repeat">Repeat</span>' : ''}
                    </div>
                    <div class="playlist-duration" data-playlist-name="${playlist.name}">
                        <i class="fas fa-clock"></i> <span class="duration-text">Calculating duration...</span>
                    </div>
                </div>
                <div class="playlist-files">
                    ${fileListHtml}
                </div>
                <div class="playlist-controls">
                    <button class="btn btn-primary" onclick="app.playPlaylist('${playlist.name}')">
                        <i class="fas fa-play"></i> Play
                    </button>
                </div>
            `;
            grid.appendChild(card);
        });

        // Load total durations for local files only
        setTimeout(() => {
            this.loadPlaylistTotalDurations();
        }, 0);
    }

    generatePlaylistFileList(items) {
        if (!items || items.length === 0) {
            return '<div class="playlist-empty">No files in playlist</div>';
        }

        let html = '<div class="playlist-file-list">';

        items.forEach((item) => {
            const fileIcon = this.getFileIcon(item.path);
            const isLocal = !item.is_webdav;
            const decodedFileName = this.decodeFileName(item.name);

            html += `
                <div class="playlist-file-item ${!item.enabled ? 'disabled' : ''}" data-file-path="${item.path}" data-is-webdav="${item.is_webdav}" data-webdav-server="${item.webdav_server || 1}">
                    <div class="file-info">
                        <div class="file-icon">${fileIcon}</div>
                        <div class="file-details">
                            <div class="file-name" title="${decodedFileName}">${decodedFileName}</div>
                            <div class="file-meta">
                                ${isLocal ?
                                    `<span class="file-duration" data-file-path="${item.path}" data-is-webdav="false" data-webdav-server="1">
                                        <i class="fas fa-clock"></i> Loading...
                                    </span>` :
                                    `<span class="file-type">
                                        <i class="fas fa-cloud"></i> Network file
                                    </span>`
                                }
                            </div>
                        </div>
                    </div>
                    <div class="file-status">
                        ${item.enabled ? '<i class="fas fa-check-circle text-success"></i>' : '<i class="fas fa-times-circle text-muted"></i>'}
                    </div>
                </div>
            `;
        });

        html += '</div>';

        // Add event listeners and load durations after the HTML is inserted
        setTimeout(() => {
            this.addPlaylistFileEventListeners();
            this.loadPlaylistFileDurations();
        }, 0);

        return html;
    }

    getFileIcon(filePath) {
        if (!filePath || typeof filePath !== 'string') {
            return '<i class="fas fa-file"></i>';
        }
        const ext = filePath.split('.').pop().toLowerCase();
        const audioExts = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a', 'aiff', 'au', 'ra'];
        const videoExts = ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm', 'm4v', '3gp', 'mpg', 'mpeg'];

        if (audioExts.includes(ext)) {
            return '<i class="fas fa-music"></i>';
        } else if (videoExts.includes(ext)) {
            return '<i class="fas fa-video"></i>';
        } else {
            return '<i class="fas fa-file"></i>';
        }
    }

    async loadPlaylistFileDurations() {
        const durationElements = document.querySelectorAll('.file-duration[data-file-path]');

        for (const element of durationElements) {
            const filePath = element.getAttribute('data-file-path');
            const isWebdav = element.getAttribute('data-is-webdav') === 'true';
            const webdavServer = parseInt(element.getAttribute('data-webdav-server')) || 1;

            // Only process local files
            if (isWebdav) {
                element.style.display = 'none';
                continue;
            }

            try {
                const response = await this.apiCall('/file-duration', {
                    method: 'POST',
                    body: JSON.stringify({
                        path: filePath,
                        is_webdav: false,
                        webdav_server: 1
                    })
                });

                if (response.success && response.duration) {
                    const formattedDuration = this.formatTime(response.duration);
                    element.innerHTML = `<i class="fas fa-clock"></i> ${formattedDuration}`;
                } else {
                    // Hide duration if it cannot be retrieved
                    element.style.display = 'none';
                }
            } catch (error) {
                console.warn(`Could not get duration for ${filePath}:`, error);
                // Hide duration on error
                element.style.display = 'none';
            }
        }
    }

    async loadPlaylistTotalDurations() {
        const playlistDurationElements = document.querySelectorAll('.playlist-duration[data-playlist-name]');

        for (const element of playlistDurationElements) {
            const playlistName = element.getAttribute('data-playlist-name');
            const playlist = this.playlists[playlistName];

            if (!playlist) continue;

            // Filter only local files
            const localFiles = playlist.items.filter(item => !item.is_webdav && item.enabled);

            if (localFiles.length === 0) {
                element.querySelector('.duration-text').textContent = 'No local files';
                continue;
            }

            let totalDuration = 0;
            let processedFiles = 0;

            element.querySelector('.duration-text').textContent = `Calculating... (0/${localFiles.length})`;

            for (const item of localFiles) {
                try {
                    const response = await this.apiCall('/file-duration', {
                        method: 'POST',
                        body: JSON.stringify({
                            path: item.path,
                            is_webdav: false,
                            webdav_server: 1
                        })
                    });

                    if (response.success && response.duration) {
                        totalDuration += response.duration;
                    }

                    processedFiles++;
                    element.querySelector('.duration-text').textContent = `Calculating... (${processedFiles}/${localFiles.length})`;

                } catch (error) {
                    console.warn(`Could not get duration for ${item.path}:`, error);
                    processedFiles++;
                }
            }

            // Display final total duration
            if (totalDuration > 0) {
                const formattedDuration = this.formatTime(totalDuration);
                element.querySelector('.duration-text').textContent = `Total: ${formattedDuration} (${localFiles.length} local files)`;
            } else {
                element.querySelector('.duration-text').textContent = 'Duration unavailable';
            }
        }
    }

    addPlaylistFileEventListeners() {
        // Add double-click event listeners to playlist file items
        const fileItems = document.querySelectorAll('.playlist-file-item');
        fileItems.forEach(item => {
            if (!item.classList.contains('disabled')) {
                item.addEventListener('dblclick', (e) => {
                    e.preventDefault();
                    const filePath = item.dataset.filePath;
                    const isWebdav = item.dataset.isWebdav === 'true';
                    const webdavServer = parseInt(item.dataset.webdavServer) || 1;

                    this.playPlaylistFile({
                        path: filePath,
                        name: item.querySelector('.file-name').textContent,
                        is_webdav: isWebdav,
                        webdav_server: webdavServer
                    });
                });

                // Add visual feedback on hover
                item.style.cursor = 'pointer';
                item.title = 'Double-click to play this file';
            }
        });
    }

    async playPlaylistFile(file) {
        try {
            const decodedFileName = this.decodeFileName(file.name);
            this.showNotification(`Loading: ${decodedFileName}`, 'info');

            const result = await this.apiCall('/vlc/play-file', {
                method: 'POST',
                body: JSON.stringify({
                    path: file.path,
                    is_webdav: file.is_webdav,
                    webdav_server: file.webdav_server,
                    volume: 70  // Default volume
                })
            });

            if (result.success) {
                const decodedResultFileName = this.decodeFileName(result.file_name);
                this.showNotification(`Now playing: ${decodedResultFileName}`, 'success');
            } else {
                this.showNotification(`Error playing file: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showNotification(`Error playing file: ${error.message}`, 'error');
        }
    }

    showPlaylistModal(playlistName = null) {
        const modal = document.getElementById('playlistModal');
        const title = document.getElementById('playlistModalTitle');
        const form = document.getElementById('playlistForm');

        if (playlistName) {
            title.textContent = 'Edit Playlist';
            this.populatePlaylistForm(this.playlists[playlistName]);
        } else {
            title.textContent = 'Add Playlist';
            form.reset();
            document.getElementById('playlistItems').innerHTML = '';
            document.getElementById('playlistVolumeValue').textContent = '70%';
        }

        modal.classList.add('active');
    }

    hidePlaylistModal() {
        document.getElementById('playlistModal').classList.remove('active');
    }

    populatePlaylistForm(playlist) {
        document.getElementById('playlistName').value = playlist.name;
        document.getElementById('playlistVolume').value = playlist.volume;
        document.getElementById('playlistVolumeValue').textContent = playlist.volume + '%';
        document.getElementById('playlistShuffle').checked = playlist.shuffle;
        document.getElementById('playlistRepeat').checked = playlist.repeat;

        const itemsContainer = document.getElementById('playlistItems');
        itemsContainer.innerHTML = '';

        playlist.items.forEach(item => {
            this.addPlaylistItemToForm(item);
        });
    }

    addPlaylistItem() {
        this.addPlaylistItemToForm();
    }

    addPlaylistItemToForm(item = null) {
        const container = document.getElementById('playlistItems');
        const itemDiv = document.createElement('div');
        itemDiv.className = 'playlist-item-form';
        itemDiv.innerHTML = `
            <div class="form-row">
                <div class="form-group">
                    <input type="text" placeholder="Item name" value="${item?.name || ''}" class="item-name">
                </div>
                <div class="form-group">
                    <input type="text" placeholder="File path" value="${item?.path || ''}" class="item-path">
                </div>
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" class="item-webdav" ${item?.is_webdav ? 'checked' : ''}>
                        WebDAV
                    </label>
                </div>
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" class="item-enabled" ${item?.enabled !== false ? 'checked' : ''}>
                        Enabled
                    </label>
                </div>
                <div class="form-group">
                    <button type="button" class="btn btn-sm btn-danger" onclick="this.parentElement.parentElement.parentElement.remove()">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
        container.appendChild(itemDiv);
    }

    async savePlaylist() {
        try {
            const form = document.getElementById('playlistForm');
            const formData = new FormData(form);

            const playlist = {
                name: document.getElementById('playlistName').value,
                volume: parseInt(document.getElementById('playlistVolume').value),
                shuffle: document.getElementById('playlistShuffle').checked,
                repeat: document.getElementById('playlistRepeat').checked,
                items: []
            };

            // Collect playlist items
            const itemForms = document.querySelectorAll('.playlist-item-form');
            itemForms.forEach(itemForm => {
                const name = itemForm.querySelector('.item-name').value;
                const path = itemForm.querySelector('.item-path').value;
                const isWebdav = itemForm.querySelector('.item-webdav').checked;
                const enabled = itemForm.querySelector('.item-enabled').checked;

                if (name && path) {
                    playlist.items.push({
                        name: name,
                        path: path,
                        is_webdav: isWebdav,
                        enabled: enabled
                    });
                }
            });

            const result = await this.apiCall('/playlists', {
                method: 'POST',
                body: JSON.stringify(playlist)
            });

            if (result.success) {
                this.showNotification('Playlist saved successfully', 'success');
                this.hidePlaylistModal();
                this.loadPlaylists();
            } else {
                this.showNotification('Error saving playlist', 'error');
            }
        } catch (error) {
            this.showNotification(`Error saving playlist: ${error.message}`, 'error');
        }
    }

    async playPlaylist(name) {
        try {
            const result = await this.apiCall(`/playlists/${name}/play`, { method: 'POST' });
            if (result.success) {
                this.showNotification(`Playing playlist: ${name}`, 'success');
            } else {
                this.showNotification(`Error playing playlist: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showNotification(`Error playing playlist: ${error.message}`, 'error');
        }
    }

    editPlaylist(name) {
        this.showPlaylistModal(name);
    }

    async deletePlaylist(name) {
        if (confirm(`Are you sure you want to delete playlist "${name}"?`)) {
            try {
                const result = await this.apiCall(`/playlists/${name}`, { method: 'DELETE' });
                if (result.success) {
                    this.showNotification('Playlist deleted successfully', 'success');
                    this.loadPlaylists();
                } else {
                    this.showNotification('Error deleting playlist', 'error');
                }
            } catch (error) {
                this.showNotification(`Error deleting playlist: ${error.message}`, 'error');
            }
        }
    }

    // Schedule Management Methods
    async loadSchedules() {
        try {
            this.schedules = await this.apiCall('/schedules');
            this.renderSchedules();
            this.updateSchedulePlaylistOptions();
        } catch (error) {
            this.showNotification('Error loading schedules', 'error');
        }
    }

    renderSchedules() {
        const list = document.getElementById('schedulesList');
        list.innerHTML = '';

        this.schedules.forEach(schedule => {
            const item = document.createElement('div');
            item.className = `schedule-item ${schedule.enabled ? 'enabled' : 'disabled'}`;

            const daysText = this.formatDaysOfWeek(schedule.days_of_week);
            const statusIcon = schedule.enabled ?
                '<i class="fas fa-check-circle text-success"></i>' :
                '<i class="fas fa-times-circle text-muted"></i>';

            item.innerHTML = `
                <div class="schedule-info">
                    <div class="schedule-header">
                        <h4>${schedule.name}</h4>
                        <div class="schedule-status">
                            ${statusIcon}
                            <span class="status-text ${schedule.enabled ? 'enabled' : 'disabled'}">
                                ${schedule.enabled ? 'Enabled' : 'Disabled'}
                            </span>
                        </div>
                    </div>
                    <div class="schedule-details">
                        <span class="schedule-time">
                            <i class="fas fa-clock"></i> ${schedule.start_time} - ${schedule.end_time}
                        </span>
                        <span class="schedule-playlist">
                            <i class="fas fa-list"></i> ${schedule.playlist_name}
                        </span>
                        <span class="schedule-days">
                            <i class="fas fa-calendar"></i> ${daysText}
                        </span>
                        ${schedule.volume ? `<span class="schedule-volume"><i class="fas fa-volume-up"></i> ${schedule.volume}%</span>` : ''}
                    </div>
                </div>
                <div class="schedule-actions">
                    <button class="btn btn-sm btn-secondary" onclick="app.editSchedule('${schedule.name}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="app.deleteSchedule('${schedule.name}')">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="btn btn-sm btn-primary" onclick="app.forceStartSchedule('${schedule.name}')">
                        <i class="fas fa-play"></i> Start Now
                    </button>
                </div>
            `;
            list.appendChild(item);
        });
    }

    formatDaysOfWeek(days) {
        if (!days || days.length === 0) return 'All days';
        if (days.length === 7) return 'All days';

        const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
        return days.map(day => dayNames[day]).join(', ');
    }

    updateSchedulePlaylistOptions() {
        const select = document.getElementById('schedulePlaylist');
        select.innerHTML = '<option value="">Select a playlist...</option>';

        Object.keys(this.playlists).forEach(name => {
            const option = document.createElement('option');
            option.value = name;
            option.textContent = name;
            select.appendChild(option);
        });
    }

    showScheduleModal(scheduleName = null) {
        const modal = document.getElementById('scheduleModal');
        const title = document.getElementById('scheduleModalTitle');
        const form = document.getElementById('scheduleForm');

        if (scheduleName) {
            title.textContent = 'Edit Schedule';
            const schedule = this.schedules.find(s => s.name === scheduleName);
            if (schedule) {
                this.populateScheduleForm(schedule);
            }
        } else {
            title.textContent = 'Add Schedule';
            form.reset();
            document.getElementById('scheduleVolumeValue').textContent = '70%';
            // Clear all day checkboxes
            this.clearAllDays();
        }

        // Setup quick day selection buttons
        this.setupDaySelectionButtons();

        modal.classList.add('active');
    }

    setupDaySelectionButtons() {
        // Remove existing event listeners to prevent duplicates
        const weekdaysBtn = document.getElementById('selectWeekdays');
        const weekendsBtn = document.getElementById('selectWeekends');
        const allDaysBtn = document.getElementById('selectAllDays');
        const clearAllBtn = document.getElementById('clearAllDays');

        // Check if elements exist before trying to replace them
        if (weekdaysBtn && weekendsBtn && allDaysBtn && clearAllBtn) {
            // Clone and replace to remove existing listeners
            weekdaysBtn.replaceWith(weekdaysBtn.cloneNode(true));
            weekendsBtn.replaceWith(weekendsBtn.cloneNode(true));
            allDaysBtn.replaceWith(allDaysBtn.cloneNode(true));
            clearAllBtn.replaceWith(clearAllBtn.cloneNode(true));

            // Add new event listeners
            document.getElementById('selectWeekdays').addEventListener('click', () => this.selectWeekdays());
            document.getElementById('selectWeekends').addEventListener('click', () => this.selectWeekends());
            document.getElementById('selectAllDays').addEventListener('click', () => this.selectAllDays());
            document.getElementById('clearAllDays').addEventListener('click', () => this.clearAllDays());
        }
    }

    selectWeekdays() {
        this.clearAllDays();
        // Monday (0) to Friday (4)
        for (let i = 0; i <= 4; i++) {
            const checkbox = document.querySelector(`input[name="days"][value="${i}"]`);
            if (checkbox) checkbox.checked = true;
        }
    }

    selectWeekends() {
        this.clearAllDays();
        // Saturday (5) and Sunday (6)
        [5, 6].forEach(day => {
            const checkbox = document.querySelector(`input[name="days"][value="${day}"]`);
            if (checkbox) checkbox.checked = true;
        });
    }

    selectAllDays() {
        // Monday (0) to Sunday (6)
        for (let i = 0; i <= 6; i++) {
            const checkbox = document.querySelector(`input[name="days"][value="${i}"]`);
            if (checkbox) checkbox.checked = true;
        }
    }

    clearAllDays() {
        const dayCheckboxes = document.querySelectorAll('input[name="days"]');
        dayCheckboxes.forEach(checkbox => checkbox.checked = false);
    }

    hideScheduleModal() {
        document.getElementById('scheduleModal').classList.remove('active');
    }

    populateScheduleForm(schedule) {
        document.getElementById('scheduleName').value = schedule.name;
        document.getElementById('scheduleStartTime').value = schedule.start_time;
        document.getElementById('scheduleEndTime').value = schedule.end_time;
        document.getElementById('schedulePlaylist').value = schedule.playlist_name;
        document.getElementById('scheduleVolume').value = schedule.volume || 70;
        document.getElementById('scheduleVolumeValue').textContent = (schedule.volume || 70) + '%';
        document.getElementById('scheduleEnabled').checked = schedule.enabled;

        // Set days of week
        const dayCheckboxes = document.querySelectorAll('input[name="days"]');
        dayCheckboxes.forEach(checkbox => {
            checkbox.checked = schedule.days_of_week.includes(parseInt(checkbox.value));
        });
    }

    async saveSchedule() {
        try {
            const schedule = {
                name: document.getElementById('scheduleName').value,
                start_time: document.getElementById('scheduleStartTime').value,
                end_time: document.getElementById('scheduleEndTime').value,
                playlist_name: document.getElementById('schedulePlaylist').value,
                volume: parseInt(document.getElementById('scheduleVolume').value),
                enabled: document.getElementById('scheduleEnabled').checked,
                days_of_week: []
            };

            // Collect selected days
            const dayCheckboxes = document.querySelectorAll('input[name="days"]:checked');
            schedule.days_of_week = Array.from(dayCheckboxes).map(cb => parseInt(cb.value));

            const result = await this.apiCall('/schedules', {
                method: 'POST',
                body: JSON.stringify(schedule)
            });

            if (result.success) {
                this.showNotification('Schedule saved successfully', 'success');
                this.hideScheduleModal();
                this.loadSchedules();
            } else {
                this.showNotification('Error saving schedule', 'error');
            }
        } catch (error) {
            this.showNotification(`Error saving schedule: ${error.message}`, 'error');
        }
    }

    editSchedule(name) {
        this.showScheduleModal(name);
    }

    async deleteSchedule(name) {
        if (confirm(`Are you sure you want to delete schedule "${name}"?`)) {
            try {
                const result = await this.apiCall(`/schedules/${name}`, { method: 'DELETE' });
                if (result.success) {
                    this.showNotification('Schedule deleted successfully', 'success');
                    this.loadSchedules();
                } else {
                    this.showNotification('Error deleting schedule', 'error');
                }
            } catch (error) {
                this.showNotification(`Error deleting schedule: ${error.message}`, 'error');
            }
        }
    }

    async forceStartSchedule(name) {
        try {
            const result = await this.apiCall(`/scheduler/force-start/${name}`, { method: 'POST' });
            if (result.success) {
                this.showNotification(`Started schedule: ${name}`, 'success');
            } else {
                this.showNotification('Error starting schedule', 'error');
            }
        } catch (error) {
            this.showNotification(`Error starting schedule: ${error.message}`, 'error');
        }
    }

    // File Browser Methods
    async browseLocal(path = null) {
        try {
            if (path === null) {
                path = document.getElementById('localPath').value || 'data';
            }

            const result = await this.apiCall(`/files/local?path=${encodeURIComponent(path)}`);

            if (result.success === false) {
                this.showNotification(`Error: ${result.error}`, 'error');
                return;
            }

            // Update browser state
            this.currentPath.local = result.current_path;
            this.currentFiles.local = result.files || [];

            // Add to history if it's a new path
            if (this.browserHistory.local[this.browserHistoryIndex.local] !== result.current_path) {
                this.browserHistory.local = this.browserHistory.local.slice(0, this.browserHistoryIndex.local + 1);
                this.browserHistory.local.push(result.current_path);
                this.browserHistoryIndex.local = this.browserHistory.local.length - 1;
            }

            // Update UI
            document.getElementById('localPath').value = result.current_path;
            this.updateBreadcrumb('local', result.current_path);
            this.applyFilters();
            this.updateNavigationButtons();

        } catch (error) {
            this.showNotification(`Error browsing local files: ${error.message}`, 'error');
        }
    }

    async browseWebdav(path = null) {
        try {
            if (path === null) {
                path = document.getElementById('webdavPath').value || '/';
            }

            const result = await this.apiCall(`/files/webdav?path=${encodeURIComponent(path)}`);

            if (result.success === false) {
                this.showNotification(`WebDAV Error: ${result.error}`, 'error');
                this.renderWebdavError(result.error);
                return;
            }

            // Update browser state
            this.currentPath.webdav = result.current_path;
            this.currentFiles.webdav = result.files || [];

            // Add to history if it's a new path
            if (this.browserHistory.webdav[this.browserHistoryIndex.webdav] !== result.current_path) {
                this.browserHistory.webdav = this.browserHistory.webdav.slice(0, this.browserHistoryIndex.webdav + 1);
                this.browserHistory.webdav.push(result.current_path);
                this.browserHistoryIndex.webdav = this.browserHistory.webdav.length - 1;
            }

            // Update UI
            document.getElementById('webdavPath').value = result.current_path;
            this.updateBreadcrumb('webdav', result.current_path);
            this.applyFilters();
            this.updateNavigationButtons();

            if (result.files.length === 0) {
                this.showNotification('Directory is empty', 'info');
            }

        } catch (error) {
            this.showNotification(`Error browsing WebDAV files: ${error.message}`, 'error');
            this.renderWebdavError(error.message);
        }
    }

    async browseWebdav2(path = null) {
        try {
            if (path === null) {
                path = document.getElementById('webdav2Path').value || '/dav';
            }

            const result = await this.apiCall(`/files/webdav2?path=${encodeURIComponent(path)}`);

            if (result.success === false) {
                this.showNotification(`WebDAV2 Error: ${result.error}`, 'error');
                this.renderWebdav2Error(result.error);
                return;
            }

            this.currentFiles.webdav2 = result.files || [];
            this.currentPath.webdav2 = result.current_path || path;

            // Add to history if it's a new path
            if (this.browserHistory.webdav2[this.browserHistoryIndex.webdav2] !== result.current_path) {
                this.browserHistory.webdav2 = this.browserHistory.webdav2.slice(0, this.browserHistoryIndex.webdav2 + 1);
                this.browserHistory.webdav2.push(result.current_path);
                this.browserHistoryIndex.webdav2 = this.browserHistory.webdav2.length - 1;
            }

            // Update UI
            document.getElementById('webdav2Path').value = this.currentPath.webdav2;
            this.applyFilters();
            this.updateNavigationButtons();
            this.updateBreadcrumb('webdav2', this.currentPath.webdav2);

            if (result.files.length === 0) {
                this.showNotification('Directory is empty', 'info');
            }

        } catch (error) {
            this.showNotification(`Error browsing WebDAV2 files: ${error.message}`, 'error');
            this.renderWebdav2Error(error.message);
        }
    }

    renderWebdavError(errorMessage) {
        const container = document.getElementById('webdavFileList');
        container.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-triangle"></i>
                <h4>WebDAV Connection Error</h4>
                <p>${errorMessage}</p>
                <p>Please check your WebDAV configuration in the Settings tab.</p>
            </div>
        `;
    }

    renderWebdav2Error(errorMessage) {
        const container = document.getElementById('webdav2FileList');
        container.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-triangle"></i>
                <h4>WebDAV2 Connection Error</h4>
                <p>${errorMessage}</p>
                <p>Please check your WebDAV2 configuration in the Settings tab.</p>
            </div>
        `;
    }

    // Navigation Methods
    navigateBack() {
        const browser = this.currentBrowser;
        if (this.browserHistoryIndex[browser] > 0) {
            this.browserHistoryIndex[browser]--;
            const path = this.browserHistory[browser][this.browserHistoryIndex[browser]];
            if (browser === 'local') {
                this.browseLocal(path);
            } else if (browser === 'webdav2') {
                this.browseWebdav2(path);
            } else {
                this.browseWebdav(path);
            }
        }
    }

    navigateForward() {
        const browser = this.currentBrowser;
        if (this.browserHistoryIndex[browser] < this.browserHistory[browser].length - 1) {
            this.browserHistoryIndex[browser]++;
            const path = this.browserHistory[browser][this.browserHistoryIndex[browser]];
            if (browser === 'local') {
                this.browseLocal(path);
            } else if (browser === 'webdav2') {
                this.browseWebdav2(path);
            } else {
                this.browseWebdav(path);
            }
        }
    }

    navigateUp() {
        const browser = this.currentBrowser;
        const currentPath = this.currentPath[browser];

        if (!currentPath || typeof currentPath !== 'string') {
            return;
        }

        let parentPath;
        if (browser === 'local') {
            // For local browser, don't allow navigation above data directory
            if (currentPath === 'data') return;
            parentPath = currentPath.split('/').slice(0, -1).join('/') || 'data';
            if (parentPath === '') parentPath = 'data';
        } else {
            // WebDAV
            if (currentPath === '/') return; // Already at root
            parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
        }

        if (browser === 'local') {
            this.browseLocal(parentPath);
        } else if (browser === 'webdav2') {
            this.browseWebdav2(parentPath);
        } else {
            this.browseWebdav(parentPath);
        }
    }

    navigateHome() {
        const browser = this.currentBrowser;
        if (browser === 'local') {
            this.browseLocal('data');
        } else if (browser === 'webdav2') {
            this.browseWebdav2('/dav');
        } else {
            this.browseWebdav('/');
        }
    }

    refreshCurrentDirectory() {
        const browser = this.currentBrowser;
        const currentPath = this.currentPath[browser];
        if (browser === 'local') {
            this.browseLocal(currentPath);
        } else if (browser === 'webdav2') {
            this.browseWebdav2(currentPath);
        } else {
            this.browseWebdav(currentPath);
        }
    }

    updateNavigationButtons() {
        const browser = this.currentBrowser;
        const backBtn = document.getElementById('backBtn');
        const forwardBtn = document.getElementById('forwardBtn');
        const upBtn = document.getElementById('upBtn');

        // Update back button
        backBtn.disabled = this.browserHistoryIndex[browser] <= 0;

        // Update forward button
        forwardBtn.disabled = this.browserHistoryIndex[browser] >= this.browserHistory[browser].length - 1;

        // Update up button
        const currentPath = this.currentPath[browser];
        if (browser === 'local') {
            upBtn.disabled = currentPath === '/' || currentPath === '.';
        } else {
            upBtn.disabled = currentPath === '/';
        }
    }

    // Breadcrumb Methods
    updateBreadcrumb(browser, path) {
        let breadcrumbId;
        if (browser === 'local') {
            breadcrumbId = 'localBreadcrumb';
        } else if (browser === 'webdav2') {
            breadcrumbId = 'webdav2Breadcrumb';
        } else {
            breadcrumbId = 'webdavBreadcrumb';
        }
        const breadcrumb = document.getElementById(breadcrumbId);

        if (!breadcrumb || !path) {
            return;
        }

        const parts = path.split('/').filter(part => part !== '');
        if (browser === 'webdav' || path.startsWith('/')) {
            parts.unshift(''); // Add root
        }

        breadcrumb.innerHTML = '';

        parts.forEach((part, index) => {
            const item = document.createElement('span');
            item.className = 'breadcrumb-item';

            if (index === 0) {
                item.textContent = browser === 'local' ? 'Home' : 'Root';
            } else {
                item.textContent = part;
            }

            if (index === parts.length - 1) {
                item.classList.add('active');
            } else {
                item.addEventListener('click', () => {
                    const targetPath = parts.slice(0, index + 1).join('/') || (browser === 'webdav' ? '/' : browser === 'webdav2' ? '/dav' : '.');
                    if (browser === 'local') {
                        this.browseLocal(targetPath);
                    } else if (browser === 'webdav2') {
                        this.browseWebdav2(targetPath);
                    } else {
                        this.browseWebdav(targetPath);
                    }
                });
            }

            breadcrumb.appendChild(item);
        });
    }

    // Filter and Search Methods
    applyFilters() {
        const browser = this.currentBrowser;
        let files = [...this.currentFiles[browser]];

        // Apply search filter
        if (this.searchQuery) {
            files = files.filter(file =>
                file.name.toLowerCase().includes(this.searchQuery.toLowerCase())
            );
        }

        // Apply type filter
        switch (this.fileTypeFilter) {
            case 'media':
                files = files.filter(file => file.is_media || file.is_dir);
                break;
            case 'audio':
                files = files.filter(file => file.is_dir || (file.is_media && this.isAudioFile(file.name)));
                break;
            case 'video':
                files = files.filter(file => file.is_dir || (file.is_media && this.isVideoFile(file.name)));
                break;
            case 'folders':
                files = files.filter(file => file.is_dir);
                break;
        }

        this.filteredFiles[browser] = files;

        // Determine the correct container ID based on browser type
        let containerId;
        if (browser === 'local') {
            containerId = 'localFileList';
        } else if (browser === 'webdav2') {
            containerId = 'webdav2FileList';
        } else {
            containerId = 'webdavFileList';
        }

        this.renderFileList(files, containerId, browser);
        this.updateFileCount(browser, files.length, this.currentFiles[browser].length);
    }

    isAudioFile(filename) {
        const audioExtensions = ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a', '.aiff', '.au', '.ra'];
        const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));
        return audioExtensions.includes(ext);
    }

    isVideoFile(filename) {
        const videoExtensions = ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.mpg', '.mpeg'];
        const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));
        return videoExtensions.includes(ext);
    }

    searchFiles() {
        this.searchQuery = document.getElementById('fileSearchInput').value;
        this.applyFilters();
    }

    updateFileCount(browser, filteredCount, totalCount) {
        let countId;
        if (browser === 'local') {
            countId = 'localFileCount';
        } else if (browser === 'webdav2') {
            countId = 'webdav2FileCount';
        } else {
            countId = 'webdavFileCount';
        }
        const countElement = document.getElementById(countId);

        let text;
        if (filteredCount === totalCount) {
            text = `${totalCount} items`;
        } else {
            text = `${filteredCount} of ${totalCount} items`;
        }

        // Add selected files count
        const selectedCount = this.selectedFiles[browser].size;
        if (selectedCount > 0) {
            text += ` • ${selectedCount} selected`;
        }

        countElement.textContent = text;
    }

    // View Methods
    setView(view) {
        this.currentView = view;

        // Update view buttons
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.view === view) {
                btn.classList.add('active');
            }
        });

        // Update file lists
        const localList = document.getElementById('localFileList');
        const webdavList = document.getElementById('webdavFileList');
        const webdav2List = document.getElementById('webdav2FileList');

        if (view === 'grid') {
            localList.classList.add('grid-view');
            webdavList.classList.add('grid-view');
            webdav2List.classList.add('grid-view');
        } else {
            localList.classList.remove('grid-view');
            webdavList.classList.remove('grid-view');
            webdav2List.classList.remove('grid-view');
        }
    }

    renderFileList(files, containerId, type) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';

        if (files.length === 0) {
            container.innerHTML = `
                <div class="empty-directory">
                    <i class="fas fa-folder-open"></i>
                    <h4>Empty Directory</h4>
                    <p>This directory contains no files or folders.</p>
                </div>
            `;
            return;
        }

        // Sort files: directories first, then by name
        const sortedFiles = [...files].sort((a, b) => {
            if (a.is_dir && !b.is_dir) return -1;
            if (!a.is_dir && b.is_dir) return 1;
            return a.name.localeCompare(b.name);
        });

        sortedFiles.forEach(file => {
            const item = document.createElement('div');
            item.className = 'file-item';

            let icon = 'fas fa-file';
            let extraClass = '';
            let tooltip = '';

            if (file.is_dir) {
                icon = 'fas fa-folder';
                tooltip = 'Double-click to open folder';
                extraClass = 'directory-item';
            } else if (file.is_media) {
                if (this.isAudioFile(file.name)) {
                    icon = 'fas fa-music';
                } else if (this.isVideoFile(file.name)) {
                    icon = 'fas fa-video';
                } else {
                    icon = 'fas fa-play-circle';
                }
                extraClass = 'media-file';
                tooltip = 'Double-click to play in VLC';
            }

            // Format file size and date
            const sizeText = file.is_dir ? '' : this.formatFileSize(file.size);
            const dateText = file.modified ? new Date(file.modified).toLocaleDateString() : '';
            const decodedFileName = this.decodeFileName(file.name);

            item.innerHTML = `
                ${file.is_media ? `<input type="checkbox" class="file-checkbox" data-file-path="${file.path}" data-file-name="${file.name}" data-is-webdav="${type === 'webdav'}" data-webdav-server="${type === 'webdav2' ? 2 : 1}">` : ''}
                <i class="${icon} file-icon"></i>
                <span class="file-name" title="${decodedFileName}">${decodedFileName}</span>
                <span class="file-details">
                    <span class="file-size">${sizeText}</span>
                    ${dateText ? `<span class="file-date">${dateText}</span>` : ''}
                </span>
                ${file.is_media ? '<i class="fas fa-play play-indicator" title="Double-click to play, single-click to select"></i>' : ''}
            `;

            if (extraClass) {
                item.classList.add(extraClass);
            }

            if (tooltip) {
                item.title = tooltip;
            } else if (file.is_media) {
                item.title = 'Single-click to select, double-click to play';
            }

            // Add click handlers
            if (file.is_dir) {
                item.addEventListener('dblclick', () => {
                    if (type === 'local') {
                        this.browseLocal(file.path);
                    } else if (type === 'webdav2') {
                        this.browseWebdav2(file.path);
                    } else {
                        this.browseWebdav(file.path);
                    }
                });
            } else if (file.is_media) {
                // Single click to toggle selection
                item.addEventListener('click', (e) => {
                    // Don't trigger if clicking on the checkbox itself
                    if (e.target.classList.contains('file-checkbox')) {
                        return;
                    }

                    const checkbox = item.querySelector('.file-checkbox');
                    if (checkbox) {
                        // Support Ctrl+click for multi-selection without deselecting others
                        if (!e.ctrlKey && !e.metaKey) {
                            // If not holding Ctrl/Cmd, toggle this item
                            checkbox.checked = !checkbox.checked;
                        } else {
                            // If holding Ctrl/Cmd, just toggle this item
                            checkbox.checked = !checkbox.checked;
                        }
                        this.handleFileSelection(file, type, checkbox.checked);
                    }
                });

                // Double click to play
                item.addEventListener('dblclick', (e) => {
                    // Prevent the click event from firing when double-clicking
                    e.preventDefault();
                    this.playMediaFile(file, type === 'webdav' || type === 'webdav2', type === 'webdav2' ? 2 : 1);
                });
            }

            // Add context menu (right-click) for future features
            item.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                this.showFileContextMenu(e, file, type);
            });

            // Add checkbox event listener for media files
            if (file.is_media) {
                const checkbox = item.querySelector('.file-checkbox');
                if (checkbox) {
                    checkbox.addEventListener('change', (e) => {
                        e.stopPropagation();
                        this.handleFileSelection(file, type, e.target.checked);
                    });

                    // Check if file is already selected
                    const fileKey = `${type}:${file.path}`;
                    if (this.selectedFiles[type].has(fileKey)) {
                        checkbox.checked = true;
                        item.classList.add('selected');
                    }
                }
            }

            container.appendChild(item);
        });
    }

    showFileContextMenu(event, file, type) {
        // Placeholder for context menu functionality
        // Could add features like: copy path, add to playlist, file info, etc.
        console.log('Context menu for:', file.name);
    }

    // File Selection Methods
    handleFileSelection(file, type, isSelected) {
        const fileKey = `${type}:${file.path}`;

        if (isSelected) {
            this.selectedFiles[type].add(fileKey);
        } else {
            this.selectedFiles[type].delete(fileKey);
        }

        this.updatePlaylistControls();
        this.updateFileItemSelection(file, type, isSelected);

        // Update file count to show selected files
        const filteredCount = this.filteredFiles[type].length;
        const totalCount = this.currentFiles[type].length;
        this.updateFileCount(type, filteredCount, totalCount);
    }

    updateFileItemSelection(file, type, isSelected) {
        let containerId;
        if (type === 'local') {
            containerId = 'localFileList';
        } else if (type === 'webdav2') {
            containerId = 'webdav2FileList';
        } else {
            containerId = 'webdavFileList';
        }
        const fileItems = document.querySelectorAll(`#${containerId} .file-item`);

        fileItems.forEach(item => {
            const checkbox = item.querySelector('.file-checkbox');
            if (checkbox && checkbox.dataset.filePath === file.path) {
                if (isSelected) {
                    item.classList.add('selected');
                } else {
                    item.classList.remove('selected');
                }
            }
        });
    }

    updatePlaylistControls() {
        const totalSelected = this.selectedFiles.local.size + this.selectedFiles.webdav.size + this.selectedFiles.webdav2.size;
        const hasSelection = totalSelected > 0;

        // Update button states
        document.getElementById('createPlaylistFromSelection').disabled = !hasSelection;
        document.getElementById('addToPlaylistBtn').disabled = !hasSelection;
        document.getElementById('addToPlaylistSelect').disabled = !hasSelection;
        document.getElementById('clearSelectionBtn').disabled = !hasSelection;

        // Update playlist dropdown
        this.updatePlaylistDropdown();
    }

    updatePlaylistDropdown() {
        const select = document.getElementById('addToPlaylistSelect');
        select.innerHTML = '<option value="">Add to Playlist...</option>';

        Object.keys(this.playlists).forEach(name => {
            const option = document.createElement('option');
            option.value = name;
            option.textContent = name;
            select.appendChild(option);
        });
    }

    selectAllMediaFiles() {
        const currentBrowser = this.currentBrowser;
        const files = this.filteredFiles[currentBrowser];

        files.forEach(file => {
            if (file.is_media) {
                const fileKey = `${currentBrowser}:${file.path}`;
                this.selectedFiles[currentBrowser].add(fileKey);
            }
        });

        this.updatePlaylistControls();
        this.refreshFileSelection();

        // Update file count to show selected files
        const filteredCount = this.filteredFiles[currentBrowser].length;
        const totalCount = this.currentFiles[currentBrowser].length;
        this.updateFileCount(currentBrowser, filteredCount, totalCount);
    }

    clearFileSelection() {
        this.selectedFiles.local.clear();
        this.selectedFiles.webdav.clear();
        this.selectedFiles.webdav2.clear();
        this.updatePlaylistControls();
        this.refreshFileSelection();

        // Update file counts to remove selected count
        ['local', 'webdav', 'webdav2'].forEach(type => {
            if (this.filteredFiles[type] && this.currentFiles[type]) {
                const filteredCount = this.filteredFiles[type].length;
                const totalCount = this.currentFiles[type].length;
                this.updateFileCount(type, filteredCount, totalCount);
            }
        });
    }

    refreshFileSelection() {
        // Update checkboxes and visual selection
        ['local', 'webdav', 'webdav2'].forEach(type => {
            let containerId;
            if (type === 'local') {
                containerId = 'localFileList';
            } else if (type === 'webdav2') {
                containerId = 'webdav2FileList';
            } else {
                containerId = 'webdavFileList';
            }
            const checkboxes = document.querySelectorAll(`#${containerId} .file-checkbox`);

            checkboxes.forEach(checkbox => {
                const fileKey = `${type}:${checkbox.dataset.filePath}`;
                const isSelected = this.selectedFiles[type].has(fileKey);
                checkbox.checked = isSelected;

                const item = checkbox.closest('.file-item');
                if (isSelected) {
                    item.classList.add('selected');
                } else {
                    item.classList.remove('selected');
                }
            });
        });
    }

    // Playlist Modal Methods
    showQuickPlaylistModal() {
        const selectedFiles = this.getSelectedFilesArray();
        if (selectedFiles.length === 0) {
            this.showNotification('Please select some media files first', 'warning');
            return;
        }

        // Update selected files preview
        this.updateSelectedFilesPreview(selectedFiles);

        // Reset form
        document.getElementById('quickPlaylistForm').reset();
        document.getElementById('quickPlaylistVolumeValue').textContent = '70%';

        // Show modal
        document.getElementById('quickPlaylistModal').classList.add('active');
    }

    hideQuickPlaylistModal() {
        document.getElementById('quickPlaylistModal').classList.remove('active');
    }

    updateSelectedFilesPreview(selectedFiles) {
        const preview = document.getElementById('selectedFilesPreview');
        const count = document.getElementById('selectedFileCount');

        count.textContent = selectedFiles.length;
        preview.innerHTML = '';

        selectedFiles.forEach(file => {
            const item = document.createElement('div');
            item.className = 'selected-file-item';

            let icon = 'fas fa-music';
            if (this.isVideoFile(file.name)) {
                icon = 'fas fa-video';
            }

            const decodedFileName = this.decodeFileName(file.name);

            item.innerHTML = `
                <i class="${icon} file-icon"></i>
                <span class="file-name" title="${file.path}">${decodedFileName}</span>
            `;

            preview.appendChild(item);
        });
    }

    getSelectedFilesArray() {
        const files = [];

        ['local', 'webdav', 'webdav2'].forEach(type => {
            this.selectedFiles[type].forEach(fileKey => {
                if (!fileKey || typeof fileKey !== 'string') return;
                const [, filePath] = fileKey.split(':');
                const file = this.findFileByPath(filePath, type);
                if (file) {
                    files.push({
                        name: file.name,
                        path: file.path,
                        is_webdav: type === 'webdav' || type === 'webdav2',
                        webdav_server: type === 'webdav2' ? 2 : 1
                    });
                }
            });
        });

        return files;
    }

    findFileByPath(path, type) {
        return this.currentFiles[type].find(file => file.path === path);
    }

    async createPlaylistFromSelection() {
        const form = document.getElementById('quickPlaylistForm');
        const formData = new FormData(form);
        const selectedFiles = this.getSelectedFilesArray();

        if (!formData.get('name')) {
            this.showNotification('Please enter a playlist name', 'warning');
            return;
        }

        const playlistData = {
            name: formData.get('name'),
            volume: parseInt(formData.get('volume')),
            shuffle: formData.has('shuffle'),
            repeat: formData.has('repeat'),
            items: selectedFiles
        };

        try {
            const result = await this.apiCall('/playlists', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(playlistData)
            });

            if (result.success) {
                this.showNotification(`Created playlist "${playlistData.name}" with ${selectedFiles.length} files`, 'success');
                this.hideQuickPlaylistModal();
                this.clearFileSelection();
                this.loadPlaylists(); // Refresh playlists
            } else {
                this.showNotification(`Error creating playlist: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showNotification(`Error creating playlist: ${error.message}`, 'error');
        }
    }

    async addSelectedFilesToPlaylist() {
        const playlistName = document.getElementById('addToPlaylistSelect').value;
        if (!playlistName) {
            this.showNotification('Please select a playlist', 'warning');
            return;
        }

        const selectedFiles = this.getSelectedFilesArray();
        if (selectedFiles.length === 0) {
            this.showNotification('No files selected', 'warning');
            return;
        }

        try {
            const result = await this.apiCall(`/playlists/${playlistName}/add-files`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ files: selectedFiles })
            });

            if (result.success) {
                this.showNotification(result.message, 'success');
                this.clearFileSelection();
                this.loadPlaylists(); // Refresh playlists
            } else {
                this.showNotification(`Error adding files: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showNotification(`Error adding files: ${error.message}`, 'error');
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Helper function to decode URL-encoded file names
    decodeFileName(fileName) {
        try {
            // Check if the string contains URL encoding (% followed by hex digits)
            if (fileName && fileName.includes('%')) {
                return decodeURIComponent(fileName);
            }
            return fileName;
        } catch (error) {
            // If decoding fails, return the original string
            console.warn('Failed to decode file name:', fileName, error);
            return fileName;
        }
    }

    async playMediaFile(file, isWebdav, webdavServer = 1) {
        // Play the selected media file directly in VLC
        try {
            const decodedFileName = this.decodeFileName(file.name);
            this.showNotification(`Loading: ${decodedFileName}`, 'info');

            const result = await this.apiCall('/vlc/play-file', {
                method: 'POST',
                body: JSON.stringify({
                    path: file.path,
                    is_webdav: isWebdav,
                    webdav_server: webdavServer,
                    volume: 70  // Default volume
                })
            });

            if (result.success) {
                const decodedResultFileName = this.decodeFileName(result.file_name);
                this.showNotification(`Now playing: ${decodedResultFileName}`, 'success');
            } else {
                this.showNotification(`Error playing file: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showNotification(`Error playing file: ${error.message}`, 'error');
        }
    }

    // Legacy method for backward compatibility
    async addFileToCurrentPlaylist(file, isWebdav, webdavServer = 1) {
        return this.playMediaFile(file, isWebdav, webdavServer);
    }

    // WebDAV Configuration Methods
    async loadWebdavConfig() {
        try {
            const config = await this.apiCall('/webdav/config');
            document.getElementById('webdavUrl').value = config.url || '';
            document.getElementById('webdavUsername').value = config.username || '';
            document.getElementById('webdavEnabled').checked = config.enabled || false;

            // Update WebDAV status display
            this.updateWebdavStatusDisplay(config);
        } catch (error) {
            this.showNotification('Error loading WebDAV config', 'error');
        }
    }

    updateWebdavStatusDisplay(config) {
        const statusDot = document.getElementById('webdavStatusDot');
        const statusText = document.getElementById('webdavStatusText');
        const serverUrl = document.getElementById('webdavServerUrl');
        const username = document.getElementById('webdavUsernameDisplay');

        if (statusDot && statusText) {
            if (config.connected) {
                statusDot.className = 'status-dot';
                statusText.textContent = 'Connected';
            } else if (config.enabled) {
                statusDot.className = 'status-dot warning';
                statusText.textContent = 'Enabled but not connected';
            } else {
                statusDot.className = 'status-dot error';
                statusText.textContent = 'Disabled';
            }
        }

        if (serverUrl) {
            serverUrl.textContent = config.url || 'Not configured';
        }
        if (username) {
            username.textContent = config.username || 'Not configured';
        }
    }

    async saveWebdavConfig() {
        try {
            const config = {
                url: document.getElementById('webdavUrl').value,
                username: document.getElementById('webdavUsername').value,
                password: document.getElementById('webdavPassword').value,
                enabled: document.getElementById('webdavEnabled').checked
            };

            const result = await this.apiCall('/webdav/config', {
                method: 'POST',
                body: JSON.stringify(config)
            });

            if (result.success) {
                this.showNotification('WebDAV configuration saved', 'success');
                // Clear password field for security
                document.getElementById('webdavPassword').value = '';
            } else {
                this.showNotification('Error saving WebDAV config', 'error');
            }
        } catch (error) {
            this.showNotification(`Error saving WebDAV config: ${error.message}`, 'error');
        }
    }

    async testWebdavConnection() {
        try {
            const result = await this.apiCall('/webdav/test', { method: 'POST' });
            if (result.success) {
                this.showNotification('WebDAV connection successful', 'success');
            } else {
                this.showNotification(`WebDAV connection failed: ${result.message}`, 'error');
            }
        } catch (error) {
            this.showNotification(`Error testing WebDAV connection: ${error.message}`, 'error');
        }
    }

    async reconnectWebdav() {
        try {
            this.showNotification('Reconnecting WebDAV...', 'info');
            const result = await this.apiCall('/webdav/reconnect', { method: 'POST' });
            if (result.success) {
                this.showNotification('WebDAV reconnected successfully', 'success');
                // Refresh WebDAV status
                this.loadWebdavConfig();
                // Refresh file list if currently browsing WebDAV
                if (this.currentBrowser === 'webdav') {
                    this.browseWebdav(this.currentPath.webdav);
                }
            } else {
                this.showNotification(`WebDAV reconnection failed: ${result.message}`, 'error');
            }
        } catch (error) {
            this.showNotification(`Error reconnecting WebDAV: ${error.message}`, 'error');
        }
    }

    // WebDAV2 Configuration Methods
    async loadWebdav2Config() {
        try {
            const config = await this.apiCall('/webdav2/config');
            document.getElementById('webdav2Url').value = config.url || '';
            document.getElementById('webdav2Username').value = config.username || '';
            document.getElementById('webdav2Enabled').checked = config.enabled || false;

            // Update WebDAV2 status display
            this.updateWebdav2StatusDisplay(config);
        } catch (error) {
            this.showNotification('Error loading WebDAV2 config', 'error');
        }
    }

    updateWebdav2StatusDisplay(config) {
        const statusElement = document.getElementById('webdav2Status');
        const statusText = statusElement.querySelector('.status-text');
        const statusIcon = statusElement.querySelector('i');
        const serverUrl = document.getElementById('webdav2ServerUrl');
        const username = document.getElementById('webdav2UsernameDisplay');

        if (config.connected) {
            statusElement.className = 'status-indicator connected';
            statusText.textContent = 'Connected';
            statusIcon.className = 'fas fa-circle';
        } else {
            statusElement.className = 'status-indicator disconnected';
            statusText.textContent = 'Disconnected';
            statusIcon.className = 'fas fa-circle';
        }

        serverUrl.textContent = config.url || 'Not configured';
        username.textContent = config.username || 'Not configured';
    }

    async saveWebdav2Config() {
        try {
            const config = {
                url: document.getElementById('webdav2Url').value,
                username: document.getElementById('webdav2Username').value,
                password: document.getElementById('webdav2Password').value,
                enabled: document.getElementById('webdav2Enabled').checked
            };

            const result = await this.apiCall('/webdav2/config', {
                method: 'POST',
                body: JSON.stringify(config)
            });

            if (result.success) {
                this.showNotification('WebDAV2 configuration saved', 'success');
                // Reload config to update status
                this.loadWebdav2Config();
            } else {
                this.showNotification(`Error saving WebDAV2 config: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showNotification(`Error saving WebDAV2 config: ${error.message}`, 'error');
        }
    }

    async testWebdav2Connection() {
        try {
            const result = await this.apiCall('/webdav2/test', { method: 'POST' });
            if (result.success) {
                this.showNotification('WebDAV2 connection successful', 'success');
            } else {
                this.showNotification(`WebDAV2 connection failed: ${result.message}`, 'error');
            }
        } catch (error) {
            this.showNotification(`Error testing WebDAV2 connection: ${error.message}`, 'error');
        }
    }

    async reconnectWebdav2() {
        try {
            this.showNotification('Reconnecting WebDAV2...', 'info');
            const result = await this.apiCall('/webdav2/reconnect', { method: 'POST' });
            if (result.success) {
                this.showNotification('WebDAV2 reconnected successfully', 'success');
                // Refresh WebDAV2 status
                this.loadWebdav2Config();
                // Refresh file list if currently browsing WebDAV2
                if (this.currentBrowser === 'webdav2') {
                    this.browseWebdav2(this.currentPath.webdav2);
                }
            } else {
                this.showNotification(`WebDAV2 reconnection failed: ${result.message}`, 'error');
            }
        } catch (error) {
            this.showNotification(`Error reconnecting WebDAV2: ${error.message}`, 'error');
        }
    }

    // Notification Methods
    showNotification(message, type = 'info') {
        const container = document.getElementById('notifications');
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        container.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);

        // Allow manual close
        notification.addEventListener('click', () => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        });
    }

    // Internationalization Methods
    async initializeI18n() {
        try {
            // Get current language from server
            const response = await fetch('/api/language');
            const data = await response.json();
            this.currentLanguage = data.current_language || 'en';

            // Load translations
            await this.loadTranslations(this.currentLanguage);

            // Setup language switcher
            this.setupLanguageSwitcher();

            // Apply translations
            this.applyTranslations();
        } catch (error) {
            console.error('Failed to initialize i18n:', error);
            // Fallback to English
            this.currentLanguage = 'en';
            await this.loadTranslations('en');
            this.applyTranslations();
        }
    }

    async loadTranslations(language) {
        try {
            const response = await fetch(`/api/translations/${language}`);
            if (response.ok) {
                this.translations = await response.json();
            } else {
                console.warn(`Failed to load translations for ${language}`);
                if (language !== 'en') {
                    // Fallback to English
                    await this.loadTranslations('en');
                }
            }
        } catch (error) {
            console.error('Error loading translations:', error);
        }
    }

    setupLanguageSwitcher() {
        const languageSelect = document.getElementById('languageSelect');
        if (languageSelect) {
            languageSelect.value = this.currentLanguage;
            languageSelect.addEventListener('change', async (e) => {
                await this.changeLanguage(e.target.value);
            });
        }
    }

    async changeLanguage(language) {
        try {
            const response = await fetch(`/api/language/${language}`, {
                method: 'POST'
            });

            if (response.ok) {
                this.currentLanguage = language;
                await this.loadTranslations(language);
                this.applyTranslations();
                this.showNotification(this.t('messages.operation_successful'), 'success');
            } else {
                this.showNotification(this.t('messages.operation_failed'), 'error');
            }
        } catch (error) {
            console.error('Error changing language:', error);
            this.showNotification(this.t('messages.operation_failed'), 'error');
        }
    }

    t(key, params = {}) {
        const keys = key.split('.');
        let value = this.translations;

        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                return key; // Return key if translation not found
            }
        }

        // Simple parameter substitution
        if (typeof value === 'string' && Object.keys(params).length > 0) {
            return value.replace(/\{(\w+)\}/g, (match, param) => {
                return params[param] || match;
            });
        }

        return value;
    }

    applyTranslations() {
        // Apply translations to elements with data-i18n attribute
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            const translation = this.t(key);
            if (translation !== key) {
                element.textContent = translation;
            }
        });

        // Apply translations to title attributes
        document.querySelectorAll('[data-i18n-title]').forEach(element => {
            const key = element.getAttribute('data-i18n-title');
            const translation = this.t(key);
            if (translation !== key) {
                element.setAttribute('title', translation);
            }
        });

        // Apply translations to placeholder attributes
        document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
            const key = element.getAttribute('data-i18n-placeholder');
            const translation = this.t(key);
            if (translation !== key) {
                element.setAttribute('placeholder', translation);
            }
        });
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new VLCApp();
});
