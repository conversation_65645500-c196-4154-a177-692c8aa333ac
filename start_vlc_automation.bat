@echo off
REM VLC Automation System - Windows Quick Start Script
REM This script sets up the environment and starts the VLC automation system

echo ========================================
echo VLC Automation System - Quick Start
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or later from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo Python found: 
python --version

REM Check if we're in the correct directory
if not exist "main.py" (
    echo ERROR: main.py not found in current directory
    echo Please run this script from the VLC automation project directory
    pause
    exit /b 1
)

echo.
echo Checking dependencies...

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ERROR: Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ERROR: Failed to activate virtual environment
    pause
    exit /b 1
)

REM Install/update dependencies
echo Installing dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Dependencies installed successfully!

REM Check if configuration exists
if not exist "config\config.json" (
    echo.
    echo No configuration found. Creating sample configuration...
    python main.py sample-config
    if errorlevel 1 (
        echo ERROR: Failed to create sample configuration
        pause
        exit /b 1
    )
    echo.
    echo Sample configuration created!
    echo Please edit the configuration files in the 'config' directory before starting.
    echo.
    echo Configuration files:
    echo - config\config.json: System and WebDAV settings
    echo - config\playlists.json: Playlist definitions
    echo - config\schedules.json: Schedule definitions
    echo.
    set /p continue="Press Enter to continue with default settings or Ctrl+C to exit and configure first..."
)

echo.
echo ========================================
echo Starting VLC Automation System...
echo ========================================
echo.
echo The web interface will be available at:
echo http://localhost:5000
echo.
echo Press Ctrl+C to stop the system
echo.

REM Start the application
python main.py start

REM If we get here, the application has stopped
echo.
echo VLC Automation System has stopped.
pause
