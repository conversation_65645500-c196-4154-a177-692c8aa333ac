"""
Configuration settings for VLC Automation System
"""
import os
import json
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from datetime import time

@dataclass
class WebDAVConfig:
    """WebDAV connection configuration"""
    url: str = ""
    username: str = ""
    password: str = ""
    enabled: bool = False

@dataclass
class WebDAV2Config:
    """Second WebDAV connection configuration"""
    url: str = ""
    username: str = ""
    password: str = ""
    enabled: bool = False

@dataclass
class PlaylistItem:
    """Individual playlist item configuration"""
    name: str
    path: str
    is_webdav: bool = False
    webdav_server: int = 1  # 1 for WebDAV, 2 for WebDAV2 (only used when is_webdav=True)
    enabled: bool = True

@dataclass
class Playlist:
    """Playlist configuration"""
    name: str
    items: List[PlaylistItem]
    shuffle: bool = False
    repeat: bool = False
    volume: int = 70

@dataclass
class ScheduleEntry:
    """Schedule entry configuration"""
    name: str
    start_time: str  # HH:MM format
    end_time: str    # HH:MM format
    playlist_name: str
    days_of_week: List[int]  # 0=Monday, 6=Sunday
    enabled: bool = True
    volume: Optional[int] = None

@dataclass
class SystemConfig:
    """Main system configuration"""
    vlc_path: str = ""
    web_port: int = 5000
    auto_start: bool = True
    log_level: str = "INFO"
    data_dir: str = "data"
    
class ConfigManager:
    """Manages application configuration"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        self.config_file = self.config_dir / "config.json"
        self.playlists_file = self.config_dir / "playlists.json"
        self.schedules_file = self.config_dir / "schedules.json"
        
        self._system_config = SystemConfig()
        self._webdav_config = WebDAVConfig()
        self._webdav2_config = WebDAV2Config()
        self._playlists: Dict[str, Playlist] = {}
        self._schedules: List[ScheduleEntry] = []
        
        self.load_all()
    
    def load_all(self):
        """Load all configuration files"""
        self.load_system_config()
        self.load_playlists()
        self.load_schedules()
    
    def load_system_config(self):
        """Load system configuration"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    data = json.load(f)
                    self._system_config = SystemConfig(**data.get('system', {}))
                    self._webdav_config = WebDAVConfig(**data.get('webdav', {}))
                    self._webdav2_config = WebDAV2Config(**data.get('webdav2', {}))
            except Exception as e:
                print(f"Error loading system config: {e}")
    
    def save_system_config(self):
        """Save system configuration"""
        data = {
            'system': asdict(self._system_config),
            'webdav': asdict(self._webdav_config),
            'webdav2': asdict(self._webdav2_config)
        }
        with open(self.config_file, 'w') as f:
            json.dump(data, f, indent=2)
    
    def load_playlists(self):
        """Load playlists configuration"""
        if self.playlists_file.exists():
            try:
                with open(self.playlists_file, 'r') as f:
                    data = json.load(f)
                    self._playlists = {}
                    for name, playlist_data in data.items():
                        items = [PlaylistItem(**item) for item in playlist_data['items']]
                        playlist_data['items'] = items
                        self._playlists[name] = Playlist(**playlist_data)
            except Exception as e:
                print(f"Error loading playlists: {e}")
    
    def save_playlists(self):
        """Save playlists configuration"""
        data = {}
        for name, playlist in self._playlists.items():
            playlist_dict = asdict(playlist)
            data[name] = playlist_dict
        
        with open(self.playlists_file, 'w') as f:
            json.dump(data, f, indent=2)
    
    def load_schedules(self):
        """Load schedules configuration"""
        if self.schedules_file.exists():
            try:
                with open(self.schedules_file, 'r') as f:
                    data = json.load(f)
                    self._schedules = [ScheduleEntry(**entry) for entry in data]
            except Exception as e:
                print(f"Error loading schedules: {e}")
    
    def save_schedules(self):
        """Save schedules configuration"""
        data = [asdict(schedule) for schedule in self._schedules]
        with open(self.schedules_file, 'w') as f:
            json.dump(data, f, indent=2)
    
    # Property accessors
    @property
    def system(self) -> SystemConfig:
        return self._system_config
    
    @property
    def webdav(self) -> WebDAVConfig:
        return self._webdav_config

    @property
    def webdav2(self) -> WebDAV2Config:
        return self._webdav2_config
    
    @property
    def playlists(self) -> Dict[str, Playlist]:
        return self._playlists
    
    @property
    def schedules(self) -> List[ScheduleEntry]:
        return self._schedules
    
    # Playlist management
    def add_playlist(self, playlist: Playlist):
        """Add or update a playlist"""
        self._playlists[playlist.name] = playlist
        self.save_playlists()
    
    def remove_playlist(self, name: str):
        """Remove a playlist"""
        if name in self._playlists:
            del self._playlists[name]
            self.save_playlists()
    
    def get_playlist(self, name: str) -> Optional[Playlist]:
        """Get a playlist by name"""
        return self._playlists.get(name)
    
    # Schedule management
    def add_schedule(self, schedule: ScheduleEntry):
        """Add a schedule entry"""
        self._schedules.append(schedule)
        self.save_schedules()
    
    def remove_schedule(self, name: str):
        """Remove a schedule entry"""
        self._schedules = [s for s in self._schedules if s.name != name]
        self.save_schedules()
    
    def get_schedule(self, name: str) -> Optional[ScheduleEntry]:
        """Get a schedule entry by name"""
        for schedule in self._schedules:
            if schedule.name == name:
                return schedule
        return None
    
    def update_schedule(self, name: str, updated_schedule: ScheduleEntry):
        """Update a schedule entry"""
        for i, schedule in enumerate(self._schedules):
            if schedule.name == name:
                self._schedules[i] = updated_schedule
                self.save_schedules()
                break
