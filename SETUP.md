# VLC Automation System - Setup Guide

This guide will help you set up the VLC Automation System on your computer.

## Prerequisites

### 1. Python Installation

**Windows:**
- Download Python 3.7+ from [python.org](https://www.python.org/downloads/)
- During installation, check "Add Python to PATH"
- Verify installation: Open Command Prompt and run `python --version`

**Mac:**
- Install using Homebrew: `brew install python3`
- Or download from [python.org](https://www.python.org/downloads/)
- Verify installation: Open Terminal and run `python3 --version`

### 2. VLC Media Player Installation

**Windows:**
- Download VLC from [videolan.org](https://www.videolan.org/vlc/)
- Install using the default settings
- VLC will be automatically detected by the system

**Mac:**
- Download VLC from [videolan.org](https://www.videolan.org/vlc/)
- Drag VLC to Applications folder
- VLC will be automatically detected in /Applications/VLC.app

## Installation Steps

### 1. Download the System

Download or clone the VLC Automation System to your desired directory:

```bash
# If using git
git clone <repository-url> vlc-automation
cd vlc-automation

# Or extract the downloaded ZIP file to a folder
```

### 2. Install Python Dependencies

Open a terminal/command prompt in the project directory and run:

```bash
# Install dependencies
python main.py install

# Or manually install
pip install -r requirements.txt
```

### 3. Create Initial Configuration

Generate sample configuration files:

```bash
python main.py sample-config
```

This creates:
- Sample playlist with example entries
- Sample schedule for morning playback
- Default system configuration

### 4. Start the System

```bash
python main.py start
```

The system will start and display:
```
VLC Automation System started on http://0.0.0.0:5000
Press Ctrl+C to stop the system
```

### 5. Access the Web Interface

Open your web browser and go to:
- `http://localhost:5000` (local access)
- `http://your-computer-ip:5000` (network access)

## Initial Configuration

### 1. System Settings

1. Open the web interface
2. Go to the **Settings** tab
3. Verify VLC status shows as working
4. Configure WebDAV if you plan to use network files

### 2. Create Your First Playlist

1. Go to the **Playlists** tab
2. Click **Add Playlist**
3. Enter a name (e.g., "Morning Music")
4. Add playlist items:
   - Click **Add Item**
   - Enter item name and file path
   - For local files: Use full paths like `C:\Music\song.mp3` (Windows) or `/Users/<USER>/Music/song.mp3` (Mac)
   - For WebDAV files: Check the WebDAV checkbox and use server paths
5. Set volume and options
6. Click **Save**

### 3. Create Your First Schedule

1. Go to the **Schedules** tab
2. Click **Add Schedule**
3. Enter schedule details:
   - Name: "Morning Routine"
   - Start time: "07:00"
   - End time: "08:00"
   - Select your playlist
   - Choose days of the week
4. Click **Save**

### 4. Start the Scheduler

1. In the **Scheduler** section, click **Start**
2. The scheduler will now automatically play your playlists at the scheduled times

## File Path Examples

### Windows Paths
```
C:\Users\<USER>\Music\song.mp3
D:\Media\Videos\video.mp4
\\NetworkDrive\Shared\Music\playlist\
```

### Mac Paths
```
/Users/<USER>/Music/song.mp3
/Volumes/ExternalDrive/Media/video.mp4
/Users/<USER>/Documents/Audio/
```

### WebDAV Paths
```
/Music/English/song.mp3
/Videos/Educational/video.mp4
/Audio/Stories/story.mp3
```

## WebDAV Setup

If you want to use files from a WebDAV server:

### 1. Configure WebDAV Connection

1. Go to **Settings** tab
2. Enter your WebDAV server details:
   - URL: `https://your-webdav-server.com/webdav`
   - Username: Your WebDAV username
   - Password: Your WebDAV password
3. Check **Enable WebDAV**
4. Click **Test Connection** to verify
5. Click **Save**

### 2. Browse WebDAV Files

1. Go to **File Browser** tab
2. Click **WebDAV** tab
3. Browse your server files
4. Double-click media files to add them to playlists

## Troubleshooting

### Common Issues

**"VLC not found" error:**
- Ensure VLC is installed
- On Windows: Add VLC to your PATH or reinstall VLC
- On Mac: Ensure VLC is in /Applications/

**"Port already in use" error:**
```bash
python main.py start --port 8080
```

**Permission errors:**
- On Mac/Linux: You may need to run with `sudo` for certain operations
- On Windows: Run Command Prompt as Administrator

**WebDAV connection fails:**
- Verify server URL is correct
- Check username/password
- Ensure server is accessible from your network
- Some servers require HTTPS

### Log Files

Check the log file for detailed error information:
- File location: `vlc_automation.log` in the project directory
- View recent logs: `tail -f vlc_automation.log` (Mac/Linux) or open in text editor (Windows)

### Testing the Installation

Run the test suite to verify everything is working:

```bash
python run_tests.py
```

## Advanced Configuration

### Custom Port and Host

```bash
# Run on different port
python main.py start --port 8080

# Allow network access
python main.py start --host 0.0.0.0 --port 8080

# Debug mode (for development)
python main.py start --debug
```

### Configuration Files

The system stores configuration in the `config/` directory:

- `config.json`: System settings and WebDAV configuration
- `playlists.json`: All playlist definitions
- `schedules.json`: All schedule definitions

You can manually edit these files if needed (stop the system first).

### Backup Configuration

To backup your configuration:

1. Copy the entire `config/` directory
2. Store it safely
3. To restore: Replace the `config/` directory and restart the system

## Usage Tips

### Organizing Media Files

**For Children's Content:**
```
/Music/
  ├── English/
  │   ├── songs/
  │   └── stories/
  ├── Chinese/
  │   ├── songs/
  │   └── stories/
  └── Educational/
      ├── math/
      └── science/
```

**Create Multiple Playlists:**
- "Morning English" - English songs and stories
- "Afternoon Chinese" - Chinese content
- "Evening Stories" - Bedtime stories
- "Weekend Music" - Fun music for weekends

**Schedule Examples:**
- 7:00-8:00 AM: Morning English (Mon-Fri)
- 12:00-1:00 PM: Afternoon Chinese (Mon-Fri)
- 7:00-8:00 PM: Evening Stories (Daily)
- 9:00-11:00 AM: Weekend Music (Sat-Sun)

### Network Access

To access the web interface from other devices on your network:

1. Start with network access: `python main.py start --host 0.0.0.0`
2. Find your computer's IP address
3. Access from other devices: `http://your-computer-ip:5000`

This allows you to control the system from your phone or tablet.

## Next Steps

Once you have the basic system running:

1. **Organize your media files** into logical directories
2. **Create multiple playlists** for different content types
3. **Set up schedules** for different times of day
4. **Test the automation** by setting short test schedules
5. **Customize volume levels** for different times (quieter in the evening)
6. **Use WebDAV** if you have a network storage server

The system is designed to run continuously, so you can set it up once and let it automatically play content according to your schedules.
