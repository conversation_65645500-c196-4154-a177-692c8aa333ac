# VLC Automation System

A comprehensive Python-based system for automating VLC media player with scheduled playback, web interface, and WebDAV support.

## Features

- **Automated Scheduling**: Set up time-based playback schedules with different playlists for different times of day
- **Web Interface**: Modern, minimalist web interface for configuration and control
- **VLC Integration**: Full control over VLC media player including playback, volume, and playlist management
- **WebDAV Support**: Access and stream media files from WebDAV servers
- **Cross-Platform**: Works on both Mac and Windows
- **Modular Design**: Easy to extend and customize
- **Real-time Control**: Live control of playback through web interface

## Quick Start

### 1. Install Dependencies

```bash
python main.py install
```

### 2. Create Sample Configuration

```bash
python main.py sample-config
```

### 3. Start the System

```bash
python main.py start
```

The web interface will be available at `http://localhost:5000`

## System Requirements

- Python 3.7 or higher
- VLC Media Player installed on the system
- Network access for WebDAV (optional)

## Installation

1. Clone or download this project to your desired directory
2. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Ensure VLC Media Player is installed on your system

## Configuration

### Basic Setup

1. Start the system: `python main.py start`
2. Open the web interface at `http://localhost:5000`
3. Go to the Settings tab to configure WebDAV (if needed)
4. Create playlists in the Playlists tab
5. Set up schedules in the Schedules tab

### Playlists

Create playlists containing local files or WebDAV files:

- **Local Files**: Use full file paths to your media files
- **WebDAV Files**: Configure WebDAV connection first, then browse and select files
- **Mixed Playlists**: Combine local and WebDAV files in the same playlist

### Schedules

Set up automatic playback schedules:

- **Time Range**: Set start and end times for playback
- **Days of Week**: Choose which days the schedule should run
- **Playlist**: Select which playlist to play
- **Volume**: Override playlist volume for this schedule

### WebDAV Configuration

To use WebDAV files:

1. Go to Settings tab
2. Enter your WebDAV server URL
3. Provide username and password
4. Enable WebDAV
5. Test the connection
6. Browse WebDAV files in the File Browser tab

## Usage

### Web Interface

The web interface provides several tabs:

- **Playlists**: Manage your media playlists
- **Schedules**: Configure automatic playback schedules
- **File Browser**: Browse local and WebDAV files
- **Settings**: Configure system and WebDAV settings

### Media Control

Use the media control panel to:

- Play/pause/stop current playback
- Skip to next/previous track
- Adjust volume
- View current media information

### Scheduler Control

Control the automation scheduler:

- Start/stop/pause the scheduler
- View current schedule status
- Manually trigger schedules

## Command Line Interface

```bash
# Start the system
python main.py start

# Start with custom host/port
python main.py start --host 0.0.0.0 --port 8080

# Start in debug mode
python main.py start --debug

# Check system status
python main.py status

# Install dependencies
python main.py install

# Create sample configuration
python main.py sample-config
```

## File Structure

```
vlc-automation/
├── main.py                 # Main entry point
├── requirements.txt        # Python dependencies
├── config/                 # Configuration module
│   └── settings.py        # Configuration management
├── core/                  # Core functionality
│   ├── vlc_controller.py  # VLC media player control
│   ├── webdav_client.py   # WebDAV client
│   └── scheduler.py       # Automation scheduler
├── web/                   # Web interface
│   ├── app.py            # Flask application
│   ├── templates/        # HTML templates
│   └── static/           # CSS/JS files
└── data/                 # Runtime data (created automatically)
```

## Configuration Files

The system creates configuration files in the `config/` directory:

- `config.json`: System and WebDAV settings
- `playlists.json`: Playlist definitions
- `schedules.json`: Schedule definitions

## Troubleshooting

### VLC Not Found

If VLC is not found automatically:

1. Install VLC Media Player
2. On Windows: Ensure VLC is in your PATH or specify the path in settings
3. On Mac: VLC should be automatically detected in Applications

### WebDAV Connection Issues

- Verify the WebDAV URL is correct
- Check username and password
- Ensure the WebDAV server is accessible from your network
- Some servers may require specific authentication methods

### Port Already in Use

If port 5000 is already in use:

```bash
python main.py start --port 8080
```

### Permission Issues

On some systems, you may need to run with elevated permissions for certain operations.

## Development

### Adding New Features

The system is designed to be modular and extensible:

- Add new VLC controls in `core/vlc_controller.py`
- Extend the web API in `web/app.py`
- Add new configuration options in `config/settings.py`
- Enhance the scheduler in `core/scheduler.py`

### Testing

Run the system in debug mode for development:

```bash
python main.py start --debug --log-level DEBUG
```

## License

This project is open source. Feel free to modify and distribute according to your needs.

## Support

For issues and questions:

1. Check the troubleshooting section
2. Review the log files (`vlc_automation.log`)
3. Ensure all dependencies are properly installed
