#!/usr/bin/env python3
"""
VLC Automation System - Main Entry Point
Provides command-line interface for starting/stopping the entire system
"""

import os
import sys
import signal
import argparse
import logging
import threading
import time
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import ConfigManager
from web.app import VLCWebApp

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('vlc_automation.log')
    ]
)

logger = logging.getLogger(__name__)

class VLCAutomationSystem:
    """Main system controller"""
    
    def __init__(self, config_dir="config", data_dir="data"):
        self.config_dir = Path(config_dir)
        self.data_dir = Path(data_dir)
        self.running = False
        self.web_app = None
        self.web_thread = None
        
        # Create directories
        self.config_dir.mkdir(exist_ok=True)
        self.data_dir.mkdir(exist_ok=True)
        
        # Initialize configuration
        self.config_manager = ConfigManager(str(self.config_dir))
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("VLC Automation System initialized")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down...")
        self.stop()
        sys.exit(0)
    
    def start(self, host='0.0.0.0', port=None, debug=False):
        """Start the VLC automation system"""
        if self.running:
            logger.warning("System is already running")
            return
        
        logger.info("Starting VLC Automation System...")
        
        try:
            # Initialize web application
            self.web_app = VLCWebApp(self.config_manager)
            
            # Set port
            if port is None:
                port = self.config_manager.system.web_port
            
            self.running = True
            
            # Start web server in a separate thread if not in debug mode
            if debug:
                # Run in main thread for debug mode
                self.web_app.run(host=host, port=port, debug=True)
            else:
                # Run in separate thread for production
                self.web_thread = threading.Thread(
                    target=self.web_app.run,
                    kwargs={'host': host, 'port': port, 'debug': False},
                    daemon=True
                )
                self.web_thread.start()
                
                logger.info(f"VLC Automation System started on http://{host}:{port}")
                logger.info("Press Ctrl+C to stop the system")
                
                # Keep main thread alive
                try:
                    while self.running:
                        time.sleep(1)
                except KeyboardInterrupt:
                    self.stop()
        
        except Exception as e:
            logger.error(f"Error starting system: {e}")
            self.running = False
            raise
    
    def stop(self):
        """Stop the VLC automation system"""
        if not self.running:
            return
        
        logger.info("Stopping VLC Automation System...")
        
        self.running = False
        
        # Shutdown web application
        if self.web_app:
            self.web_app.shutdown()
        
        # Wait for web thread to finish
        if self.web_thread and self.web_thread.is_alive():
            self.web_thread.join(timeout=5)
        
        logger.info("VLC Automation System stopped")
    
    def status(self):
        """Get system status"""
        status = {
            'running': self.running,
            'config_dir': str(self.config_dir),
            'data_dir': str(self.data_dir),
            'web_port': self.config_manager.system.web_port,
            'auto_start': self.config_manager.system.auto_start,
            'vlc_path': self.config_manager.system.vlc_path,
            'webdav_enabled': self.config_manager.webdav.enabled,
            'playlists_count': len(self.config_manager.playlists),
            'schedules_count': len(self.config_manager.schedules)
        }
        
        return status
    
    def install_dependencies(self):
        """Install required Python dependencies"""
        logger.info("Installing dependencies...")
        
        try:
            import subprocess
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
            ], check=True, capture_output=True, text=True)
            
            logger.info("Dependencies installed successfully")
            logger.debug(result.stdout)
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Error installing dependencies: {e}")
            logger.error(e.stderr)
            raise
        except Exception as e:
            logger.error(f"Unexpected error installing dependencies: {e}")
            raise
    
    def create_sample_config(self):
        """Create sample configuration files"""
        logger.info("Creating sample configuration...")
        
        # Create sample playlist
        from config.settings import Playlist, PlaylistItem, ScheduleEntry
        
        sample_playlist = Playlist(
            name="Sample Music",
            items=[
                PlaylistItem(
                    name="Sample Song 1",
                    path="/path/to/music/song1.mp3",
                    is_webdav=False,
                    enabled=True
                ),
                PlaylistItem(
                    name="Sample Song 2",
                    path="/path/to/music/song2.mp3",
                    is_webdav=False,
                    enabled=True
                )
            ],
            shuffle=False,
            repeat=True,
            volume=70
        )
        
        self.config_manager.add_playlist(sample_playlist)
        
        # Create sample schedule
        sample_schedule = ScheduleEntry(
            name="Morning Music",
            start_time="07:00",
            end_time="08:00",
            playlist_name="Sample Music",
            days_of_week=[0, 1, 2, 3, 4],  # Monday to Friday
            enabled=True,
            volume=60
        )
        
        self.config_manager.add_schedule(sample_schedule)
        
        logger.info("Sample configuration created")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='VLC Automation System')
    parser.add_argument('command', choices=['start', 'stop', 'status', 'install', 'sample-config'],
                       help='Command to execute')
    parser.add_argument('--host', default='0.0.0.0', help='Host to bind to (default: 0.0.0.0)')
    parser.add_argument('--port', type=int, help='Port to bind to (default: from config)')
    parser.add_argument('--debug', action='store_true', help='Run in debug mode')
    parser.add_argument('--config-dir', default='config', help='Configuration directory')
    parser.add_argument('--data-dir', default='data', help='Data directory')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='Log level')
    
    args = parser.parse_args()
    
    # Set log level
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # Initialize system
    system = VLCAutomationSystem(args.config_dir, args.data_dir)
    
    try:
        if args.command == 'start':
            system.start(host=args.host, port=args.port, debug=args.debug)
        
        elif args.command == 'stop':
            # For stop command, we would need to implement IPC or use a PID file
            print("Stop command not implemented yet. Use Ctrl+C to stop a running instance.")
        
        elif args.command == 'status':
            status = system.status()
            print("VLC Automation System Status:")
            print("-" * 40)
            for key, value in status.items():
                print(f"{key.replace('_', ' ').title()}: {value}")
        
        elif args.command == 'install':
            system.install_dependencies()
            print("Dependencies installed successfully!")
        
        elif args.command == 'sample-config':
            system.create_sample_config()
            print("Sample configuration created!")
    
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
        system.stop()
    
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
