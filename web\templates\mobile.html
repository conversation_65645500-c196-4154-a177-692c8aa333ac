<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>VLC Automation - Mobile</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#2563eb">
</head>
<body>
    <!-- Mobile Header (fully scrollable) -->
    <header class="mobile-header">
        <div class="header-top">
            <div class="app-title">
                <i class="fas fa-music"></i>
                <span data-i18n="app_title">VLC Automation</span>
            </div>
            <div class="header-actions">
                <button class="header-btn" id="languageBtn">
                    <i class="fas fa-globe"></i>
                </button>
                <a href="/desktop" class="header-btn" title="Desktop Version">
                    <i class="fas fa-desktop"></i>
                </a>
                <button class="header-btn" id="settingsBtn">
                    <i class="fas fa-cog"></i>
                </button>
                <button class="header-btn" id="logoutBtn">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-item" id="systemStatus">
                <span class="status-dot"></span>
                <span class="status-text" data-i18n="status.connecting">Connecting...</span>
            </div>
            <div class="status-item" id="schedulerStatus">
                <i class="fas fa-clock"></i>
                <span id="schedulerState">Stopped</span>
            </div>
        </div>
    </header>

    <!-- Current Media Display -->
    <section class="current-media-section" id="currentMediaSection">
        <div class="media-artwork">
            <div class="artwork-placeholder">
                <i class="fas fa-music"></i>
            </div>
        </div>
        <div class="media-info">
            <div class="media-title" id="mediaTitle">No media playing</div>
            <div class="media-details" id="mediaDetails"></div>
            <div class="playlist-info" id="currentPlaylistInfo" style="display: none;">
                <i class="fas fa-list"></i>
                <span id="currentPlaylistName">None</span>
            </div>
        </div>
    </section>

    <!-- Fixed Media Player -->
    <section class="fixed-media-player">
        <!-- Now Playing Info -->
        <div class="now-playing-info">
            <div class="media-title-compact" id="mediaTitleCompact">No media playing</div>
            <div class="progress-container-compact">
                <div class="progress-bar-compact" id="progressBarCompact">
                    <div class="progress-fill-compact" id="progressFillCompact"></div>
                </div>
                <div class="progress-time-compact">
                    <span id="currentTimeCompact">0:00</span>
                    <span>/</span>
                    <span id="totalTimeCompact">0:00</span>
                </div>
            </div>
        </div>

        <!-- Media Controls -->
        <div class="media-controls-compact">
            <button class="control-btn-compact" id="prevBtnCompact">
                <i class="fas fa-step-backward"></i>
            </button>
            <button class="control-btn-compact primary" id="playPauseBtnCompact">
                <i class="fas fa-play" id="playPauseIconCompact"></i>
            </button>
            <button class="control-btn-compact" id="nextBtnCompact">
                <i class="fas fa-step-forward"></i>
            </button>
            <button class="control-btn-compact" id="stopBtnCompact">
                <i class="fas fa-stop"></i>
            </button>
            <button class="control-btn-compact" id="repeatBtnCompact" title="Single Track Repeat">
                <i class="fas fa-redo"></i>
            </button>
            <button class="control-btn-compact volume-btn" id="volumeBtnCompact">
                <i class="fas fa-volume-up" id="volumeIconCompact"></i>
            </button>
        </div>

        <!-- Volume Slider (Hidden by default) -->
        <div class="volume-slider-overlay" id="volumeSliderOverlay" style="display: none;">
            <div class="volume-slider-content">
                <input type="range" class="volume-slider-compact" id="volumeSliderCompact" min="0" max="100" value="70">
                <span class="volume-value-compact" id="volumeValueCompact">70%</span>
            </div>
        </div>
    </section>

    <!-- Scheduler Controls -->
    <section class="scheduler-section">
        <div class="section-header">
            <h3><i class="fas fa-clock"></i> <span data-i18n="scheduler.title">Scheduler</span></h3>
            <div class="scheduler-status-badge" id="schedulerStatusBadge">
                <span class="status-dot" id="schedulerStatusDot"></span>
                <span class="status-text" id="schedulerStatusText">Stopped</span>
            </div>
        </div>

        <div class="scheduler-controls">
            <button class="btn btn-success small" id="startSchedulerBtn" title="Start Scheduler">
                <i class="fas fa-play"></i>
                <span>Start</span>
            </button>
            <button class="btn btn-warning small" id="pauseSchedulerBtn" title="Pause Scheduler">
                <i class="fas fa-pause"></i>
                <span>Pause</span>
            </button>
            <button class="btn btn-danger small" id="stopSchedulerBtn" title="Stop Scheduler">
                <i class="fas fa-stop"></i>
                <span>Stop</span>
            </button>
        </div>

        <div class="scheduler-info">
            <div class="info-item">
                <span class="label">Next:</span>
                <span class="value" id="nextSchedule">None</span>
            </div>
            <div class="info-item">
                <span class="label">Session:</span>
                <span class="value" id="currentSession">None</span>
            </div>
        </div>
    </section>

    <!-- Bottom Navigation -->
    <nav class="bottom-nav">
        <button class="nav-btn active" data-tab="playlists">
            <i class="fas fa-list"></i>
            <span data-i18n="navigation.playlists">Playlists</span>
        </button>
        <button class="nav-btn" data-tab="schedules">
            <i class="fas fa-calendar"></i>
            <span data-i18n="navigation.schedules">Schedules</span>
        </button>
        <button class="nav-btn" data-tab="files">
            <i class="fas fa-folder"></i>
            <span data-i18n="navigation.files">Files</span>
        </button>
    </nav>

    <!-- Tab Content Container -->
    <main class="tab-container">
        <!-- Playlists Tab -->
        <div class="tab-content active" id="playlistsTab">
            <div class="tab-header">
                <h2 data-i18n="navigation.playlists">Playlists</h2>
                <button class="btn btn-primary" id="addPlaylistBtn">
                    <i class="fas fa-plus"></i>
                </button>
            </div>
            <div class="content-scroll">
                <div class="playlists-grid" id="playlistsGrid">
                    <!-- Playlists will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Schedules Tab -->
        <div class="tab-content" id="schedulesTab">
            <div class="tab-header">
                <h2 data-i18n="navigation.schedules">Schedules</h2>
                <button class="btn btn-primary" id="addScheduleBtn">
                    <i class="fas fa-plus"></i>
                </button>
            </div>
            <div class="content-scroll">
                <div class="schedules-list" id="schedulesList">
                    <!-- Schedules will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Files Tab -->
        <div class="tab-content" id="filesTab">
            <div class="tab-header">
                <h2 data-i18n="navigation.files">File Browser</h2>
                <div class="file-actions">
                    <button class="btn btn-secondary small" id="selectAllBtn">
                        <i class="fas fa-check-square"></i>
                    </button>
                    <button class="btn btn-secondary small" id="clearSelectionBtn" disabled>
                        <i class="fas fa-square"></i>
                    </button>
                </div>
            </div>
            
            <!-- Browser Tabs -->
            <div class="browser-tabs">
                <button class="browser-tab active" data-browser="local">
                    <i class="fas fa-hdd"></i>
                    <span>Local</span>
                </button>
                <button class="browser-tab" data-browser="webdav">
                    <i class="fas fa-cloud"></i>
                    <span>WebDAV</span>
                </button>
                <button class="browser-tab" data-browser="webdav2">
                    <i class="fas fa-cloud"></i>
                    <span>WebDAV2</span>
                </button>
            </div>
            
            <!-- File Browser Content -->
            <div class="browser-content">
                <!-- Navigation Bar -->
                <div class="nav-bar">
                    <div class="nav-controls">
                        <button class="nav-btn" id="backBtn" title="Go Back">
                            <i class="fas fa-arrow-left"></i>
                        </button>
                        <button class="nav-btn" id="upBtn" title="Parent Directory">
                            <i class="fas fa-arrow-up"></i>
                        </button>
                        <button class="nav-btn" id="homeBtn" title="Home Directory">
                            <i class="fas fa-home"></i>
                        </button>
                        <button class="nav-btn" id="refreshBtn" title="Refresh">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <div class="path-display" id="pathDisplay">
                        <span id="currentPathText">/</span>
                    </div>
                </div>
                
                <!-- Search and Filter -->
                <div class="search-filter-bar">
                    <div class="search-box">
                        <input type="text" id="fileSearchInput" placeholder="Search files..." class="search-input">
                        <button class="search-btn" id="searchBtn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <select id="fileTypeFilter" class="filter-select">
                        <option value="all">All</option>
                        <option value="media">Media</option>
                        <option value="audio">Audio</option>
                        <option value="video">Video</option>
                        <option value="folders">Folders</option>
                    </select>
                </div>
                
                <!-- File List -->
                <div class="content-scroll">
                    <div class="file-list" id="fileList">
                        <!-- Files will be loaded here -->
                    </div>
                </div>
                
                <!-- Selection Actions -->
                <div class="selection-actions" id="selectionActions" style="display: none;">
                    <div class="selection-info">
                        <span id="selectionCount">0 selected</span>
                    </div>
                    <div class="action-buttons">
                        <button class="btn btn-primary" id="createPlaylistFromSelection">
                            <i class="fas fa-plus"></i> New Playlist
                        </button>
                        <select id="addToPlaylistSelect" class="playlist-select">
                            <option value="">Add to Playlist...</option>
                        </select>
                        <button class="btn btn-secondary" id="addToPlaylistBtn" disabled>
                            <i class="fas fa-plus-circle"></i> Add
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Mobile Modals -->

    <!-- Language Selection Modal -->
    <div class="mobile-modal" id="languageModal">
        <div class="modal-overlay" id="languageModalOverlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>Language / 语言</h3>
                <button class="modal-close" id="closeLanguageModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="language-options">
                    <button class="language-option" data-lang="en">
                        <span class="flag">🇺🇸</span>
                        <span class="name">English</span>
                    </button>
                    <button class="language-option" data-lang="zh">
                        <span class="flag">🇨🇳</span>
                        <span class="name">中文</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="mobile-modal" id="settingsModal">
        <div class="modal-overlay" id="settingsModalOverlay"></div>
        <div class="modal-content large">
            <div class="modal-header">
                <h3><i class="fas fa-cog"></i> Settings</h3>
                <button class="modal-close" id="closeSettingsModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="settings-tabs">
                    <button class="settings-tab active" data-settings-tab="webdav">WebDAV</button>
                    <button class="settings-tab" data-settings-tab="webdav2">WebDAV2</button>
                    <button class="settings-tab" data-settings-tab="system">System</button>
                </div>

                <div class="settings-content">
                    <!-- WebDAV Settings -->
                    <div class="settings-pane active" id="webdavSettings">
                        <form id="webdavForm" class="mobile-form">
                            <div class="form-group">
                                <label for="webdavUrl">WebDAV URL:</label>
                                <input type="url" id="webdavUrl" name="url" placeholder="https://your-webdav-server.com">
                            </div>
                            <div class="form-group">
                                <label for="webdavUsername">Username:</label>
                                <input type="text" id="webdavUsername" name="username">
                            </div>
                            <div class="form-group">
                                <label for="webdavPassword">Password:</label>
                                <input type="password" id="webdavPassword" name="password">
                            </div>
                            <div class="form-group">
                                <label class="switch-label">
                                    <input type="checkbox" id="webdavEnabled" name="enabled">
                                    <span class="switch-slider"></span>
                                    Enable WebDAV
                                </label>
                            </div>
                            <div class="form-actions">
                                <button type="button" class="btn btn-secondary" id="testWebdavBtn">
                                    <i class="fas fa-plug"></i> Test Connection
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save
                                </button>
                            </div>
                        </form>

                        <div class="connection-status">
                            <h4>Connection Status</h4>
                            <div class="status-info">
                                <div class="status-item">
                                    <span class="label">Status:</span>
                                    <span class="value" id="webdavConnectionStatus">
                                        <span class="status-indicator">
                                            <span class="status-dot" id="webdavStatusDot"></span>
                                            <span id="webdavStatusText">Unknown</span>
                                        </span>
                                    </span>
                                </div>
                                <div class="status-item">
                                    <span class="label">Server:</span>
                                    <span class="value" id="webdavServerUrl">Not configured</span>
                                </div>
                                <div class="status-item">
                                    <span class="label">User:</span>
                                    <span class="value" id="webdavUsernameDisplay">Not configured</span>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button type="button" class="btn btn-warning" id="reconnectWebdavBtn">
                                    <i class="fas fa-sync-alt"></i> Reconnect
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- WebDAV2 Settings -->
                    <div class="settings-pane" id="webdav2Settings">
                        <form id="webdav2Form" class="mobile-form">
                            <div class="form-group">
                                <label for="webdav2Url">WebDAV2 URL:</label>
                                <input type="url" id="webdav2Url" name="url" placeholder="https://your-second-webdav-server.com">
                            </div>
                            <div class="form-group">
                                <label for="webdav2Username">Username:</label>
                                <input type="text" id="webdav2Username" name="username">
                            </div>
                            <div class="form-group">
                                <label for="webdav2Password">Password:</label>
                                <input type="password" id="webdav2Password" name="password">
                            </div>
                            <div class="form-group">
                                <label class="switch-label">
                                    <input type="checkbox" id="webdav2Enabled" name="enabled">
                                    <span class="switch-slider"></span>
                                    Enable WebDAV2
                                </label>
                            </div>
                            <div class="form-actions">
                                <button type="button" class="btn btn-secondary" id="testWebdav2Btn">
                                    <i class="fas fa-plug"></i> Test Connection
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save
                                </button>
                            </div>
                        </form>

                        <div class="connection-status">
                            <h4>Connection Status</h4>
                            <div class="status-info">
                                <div class="status-item">
                                    <span class="label">Status:</span>
                                    <span class="value">
                                        <span class="status-indicator" id="webdav2Status">
                                            <span class="status-dot"></span>
                                            <span class="status-text">Disconnected</span>
                                        </span>
                                    </span>
                                </div>
                                <div class="status-item">
                                    <span class="label">Server:</span>
                                    <span class="value" id="webdav2ServerUrl">Not configured</span>
                                </div>
                                <div class="status-item">
                                    <span class="label">User:</span>
                                    <span class="value" id="webdav2UsernameDisplay">Not configured</span>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button type="button" class="btn btn-warning" id="reconnectWebdav2Btn">
                                    <i class="fas fa-sync-alt"></i> Reconnect
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- System Settings -->
                    <div class="settings-pane" id="systemSettings">
                        <div class="system-info">
                            <h4>System Information</h4>
                            <div class="status-info">
                                <div class="status-item">
                                    <span class="label">VLC Status:</span>
                                    <span class="value" id="vlcStatus">Unknown</span>
                                </div>
                                <div class="status-item">
                                    <span class="label">Web Port:</span>
                                    <span class="value" id="webPort">5000</span>
                                </div>
                                <div class="status-item">
                                    <span class="label">Auto Start:</span>
                                    <span class="value" id="autoStart">Enabled</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Playlist Modal -->
    <div class="mobile-modal" id="playlistModal">
        <div class="modal-overlay" id="playlistModalOverlay"></div>
        <div class="modal-content large">
            <div class="modal-header">
                <h3 id="playlistModalTitle">Add Playlist</h3>
                <button class="modal-close" id="closePlaylistModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="playlistForm" class="mobile-form">
                    <div class="form-group">
                        <label for="playlistName">Playlist Name:</label>
                        <input type="text" id="playlistName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="playlistVolume">Default Volume: <span id="playlistVolumeValue">70%</span></label>
                        <input type="range" id="playlistVolume" name="volume" min="0" max="100" value="70" class="range-slider">
                    </div>
                    <div class="form-group">
                        <label class="switch-label">
                            <input type="checkbox" id="playlistShuffle" name="shuffle">
                            <span class="switch-slider"></span>
                            Shuffle
                        </label>
                    </div>
                    <div class="form-group">
                        <label class="switch-label">
                            <input type="checkbox" id="playlistRepeat" name="repeat">
                            <span class="switch-slider"></span>
                            Repeat
                        </label>
                    </div>
                    <div class="form-group">
                        <label>Playlist Items:</label>
                        <div class="playlist-items" id="playlistItems">
                            <!-- Playlist items will be added here -->
                        </div>
                        <button type="button" class="btn btn-secondary" id="addPlaylistItem">
                            <i class="fas fa-plus"></i> Add Item
                        </button>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelPlaylistBtn">Cancel</button>
                <button type="button" class="btn btn-primary" id="savePlaylistBtn">Save</button>
            </div>
        </div>
    </div>

    <!-- Quick Playlist Creation Modal -->
    <div class="mobile-modal" id="quickPlaylistModal">
        <div class="modal-overlay" id="quickPlaylistModalOverlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create Playlist from Selected Files</h3>
                <button class="modal-close" id="closeQuickPlaylistModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="quickPlaylistForm" class="mobile-form">
                    <div class="form-group">
                        <label for="quickPlaylistName">Playlist Name:</label>
                        <input type="text" id="quickPlaylistName" name="name" required placeholder="Enter playlist name...">
                    </div>
                    <div class="form-group">
                        <label for="quickPlaylistVolume">Volume: <span id="quickPlaylistVolumeValue">70%</span></label>
                        <input type="range" id="quickPlaylistVolume" name="volume" min="0" max="100" value="70" class="range-slider">
                    </div>
                    <div class="form-group">
                        <label class="switch-label">
                            <input type="checkbox" id="quickPlaylistShuffle" name="shuffle">
                            <span class="switch-slider"></span>
                            Shuffle
                        </label>
                    </div>
                    <div class="form-group">
                        <label class="switch-label">
                            <input type="checkbox" id="quickPlaylistRepeat" name="repeat">
                            <span class="switch-slider"></span>
                            Repeat
                        </label>
                    </div>
                    <div class="form-group">
                        <label>Selected Files (<span id="selectedFileCount">0</span>):</label>
                        <div class="selected-files-preview" id="selectedFilesPreview">
                            <!-- Selected files will be shown here -->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelQuickPlaylistBtn">Cancel</button>
                <button type="button" class="btn btn-primary" id="createQuickPlaylistBtn">Create Playlist</button>
            </div>
        </div>
    </div>

    <!-- Schedule Modal -->
    <div class="mobile-modal" id="scheduleModal">
        <div class="modal-overlay" id="scheduleModalOverlay"></div>
        <div class="modal-content large">
            <div class="modal-header">
                <h3 id="scheduleModalTitle">Add Schedule</h3>
                <button class="modal-close" id="closeScheduleModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="scheduleForm" class="mobile-form">
                    <div class="form-group">
                        <label for="scheduleName">Schedule Name:</label>
                        <input type="text" id="scheduleName" name="name" required>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="scheduleStartTime">Start Time:</label>
                            <input type="time" id="scheduleStartTime" name="start_time" required>
                        </div>
                        <div class="form-group">
                            <label for="scheduleEndTime">End Time:</label>
                            <input type="time" id="scheduleEndTime" name="end_time" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="schedulePlaylist">Playlist:</label>
                        <select id="schedulePlaylist" name="playlist_name" required>
                            <option value="">Select a playlist...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="scheduleVolume">Volume Override: <span id="scheduleVolumeValue">70%</span></label>
                        <input type="range" id="scheduleVolume" name="volume" min="0" max="100" value="70" class="range-slider">
                    </div>
                    <div class="form-group">
                        <label>Days of Week:</label>
                        <div class="days-quick-select">
                            <button type="button" class="btn btn-sm btn-outline" id="selectWeekdays">Weekdays</button>
                            <button type="button" class="btn btn-sm btn-outline" id="selectWeekends">Weekends</button>
                            <button type="button" class="btn btn-sm btn-outline" id="selectAllDays">All Days</button>
                            <button type="button" class="btn btn-sm btn-outline" id="clearAllDays">Clear All</button>
                        </div>
                        <div class="days-of-week">
                            <label class="day-checkbox">
                                <input type="checkbox" name="days" value="0">
                                <span class="day-label">Mon</span>
                            </label>
                            <label class="day-checkbox">
                                <input type="checkbox" name="days" value="1">
                                <span class="day-label">Tue</span>
                            </label>
                            <label class="day-checkbox">
                                <input type="checkbox" name="days" value="2">
                                <span class="day-label">Wed</span>
                            </label>
                            <label class="day-checkbox">
                                <input type="checkbox" name="days" value="3">
                                <span class="day-label">Thu</span>
                            </label>
                            <label class="day-checkbox">
                                <input type="checkbox" name="days" value="4">
                                <span class="day-label">Fri</span>
                            </label>
                            <label class="day-checkbox">
                                <input type="checkbox" name="days" value="5">
                                <span class="day-label">Sat</span>
                            </label>
                            <label class="day-checkbox">
                                <input type="checkbox" name="days" value="6">
                                <span class="day-label">Sun</span>
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="switch-label">
                            <input type="checkbox" id="scheduleEnabled" name="enabled" checked>
                            <span class="switch-slider"></span>
                            Enabled
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelScheduleBtn">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveScheduleBtn">Save</button>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div class="mobile-notifications" id="notifications"></div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Loading...</span>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ url_for('static', filename='js/mobile.js') }}"></script>
</body>
</html>
